"""Agents package for the CatchUp agentic architecture."""

from .base_agent import (
    BaseAgent,
    AgentType,
    AgentStatus,
    AgentContext,
    AgentMetrics,
    AgentRegistry,
    agent_registry
)
from .orchestrator_agent import OrchestratorAgent, orchestrator_agent
from .error_handling_agent import <PERSON>rror<PERSON>andlingAgent, ErrorType, RecoveryStrategy
from .intent_analysis_agent import IntentAnalysisAgent
from .search_agent import SearchAgent
from .booking_agent import BookingAgent
from .communication_agent import CommunicationAgent
from .user_management_agent import UserManagementAgent
from .quality_assurance_agent import QualityAssuranceAgent

# Create and register all agents
intent_analysis_agent = IntentAnalysisAgent()
agent_registry.register_agent(intent_analysis_agent)

error_handling_agent = ErrorHandlingAgent()
agent_registry.register_agent(error_handling_agent)

search_agent = SearchAgent()
agent_registry.register_agent(search_agent)

booking_agent = BookingAgent()
agent_registry.register_agent(booking_agent)

communication_agent = CommunicationAgent()
agent_registry.register_agent(communication_agent)

user_management_agent = UserManagementAgent()
agent_registry.register_agent(user_management_agent)

quality_assurance_agent = QualityAssuranceAgent()
agent_registry.register_agent(quality_assurance_agent)

__all__ = [
    "BaseAgent",
    "AgentType",
    "AgentStatus",
    "AgentContext",
    "AgentMetrics",
    "AgentRegistry",
    "agent_registry",
    "OrchestratorAgent",
    "orchestrator_agent",
    "ErrorHandlingAgent",
    "ErrorType",
    "RecoveryStrategy",
    "IntentAnalysisAgent",
    "intent_analysis_agent",
    "error_handling_agent",
    "SearchAgent",
    "search_agent",
    "BookingAgent",
    "booking_agent",
    "CommunicationAgent",
    "communication_agent",
    "UserManagementAgent",
    "user_management_agent",
    "QualityAssuranceAgent",
    "quality_assurance_agent"
]
