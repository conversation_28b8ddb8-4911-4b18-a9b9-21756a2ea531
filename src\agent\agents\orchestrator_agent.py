"""Orchestrator Agent for traffic routing and workflow coordination."""

import uuid
from typing import Dict, Any, Optional, List
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext, agent_registry
from agent.state import CatchUpState


class OrchestratorAgent(BaseAgent):
    """Main controller agent that routes requests and coordinates workflows."""
    
    def __init__(self):
        super().__init__(AgentType.ORCHESTRATOR, "Orchestrator Agent")
        self.conversation_states: Dict[str, Dict[str, Any]] = {}
        self.active_workflows: Dict[str, List[str]] = {}  # request_id -> agent_chain
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Route requests to appropriate specialized agents."""
        
        # Generate request ID for tracking
        request_id = str(uuid.uuid4())
        
        # Get the last message to analyze
        messages = state.get("messages", [])
        if not messages:
            return {"messages": [AIMessage(content="Hello! How can I assist you today?")]}
        
        last_message = messages[-1]
        
        # Create context for agent communication
        agent_context = AgentContext(
            agent_type=self.agent_type,
            request_id=request_id,
            metadata={"session_id": state.get("session_config", {}).get("session_id")}
        )
        
        try:
            # Step 1: Intent Analysis
            intent_agent = agent_registry.get_agent(AgentType.INTENT_ANALYSIS)
            if not intent_agent:
                return await self._fallback_response(state, "Intent analysis agent not available")
            
            intent_result = await intent_agent.execute_with_metrics(state, config, agent_context)
            
            # Extract intent information
            intent_info = intent_result.get("intent_analysis", {})
            user_intent = intent_info.get("intent")
            confidence = intent_info.get("confidence", 0.0)
            required_tools = intent_info.get("required_tools", [])
            
            # Update context with intent information
            agent_context.user_intent = user_intent
            agent_context.confidence = confidence
            agent_context.required_tools = required_tools
            
            # Step 2: Route to appropriate domain agent
            domain_agent = self._select_domain_agent(user_intent, confidence)
            if not domain_agent:
                return await self._fallback_response(state, "No suitable agent found for this request")
            
            # Execute domain agent
            domain_result = await domain_agent.execute_with_metrics(state, config, agent_context)
            
            # Step 3: Quality assurance (if available)
            qa_agent = agent_registry.get_agent(AgentType.QUALITY_ASSURANCE)
            if qa_agent and confidence > 0.7:  # Only for high-confidence requests
                qa_context = AgentContext(
                    agent_type=AgentType.QUALITY_ASSURANCE,
                    request_id=request_id,
                    user_intent=user_intent,
                    metadata={
                        "original_response": domain_result,
                        "domain_agent": domain_agent.agent_type.value
                    }
                )
                qa_result = await qa_agent.execute_with_metrics(state, config, qa_context)
                
                # Use QA result if it provides improvements
                if qa_result.get("improved_response"):
                    domain_result = qa_result
            
            # Track workflow
            self.active_workflows[request_id] = [
                intent_agent.agent_type.value,
                domain_agent.agent_type.value
            ]
            
            # Add orchestration metadata
            if "messages" in domain_result:
                for message in domain_result["messages"]:
                    if hasattr(message, 'additional_kwargs'):
                        message.additional_kwargs = message.additional_kwargs or {}
                        message.additional_kwargs["orchestrator_metadata"] = {
                            "request_id": request_id,
                            "intent": user_intent,
                            "confidence": confidence,
                            "agent_chain": self.active_workflows[request_id]
                        }
            
            return domain_result
            
        except Exception as e:
            # Route to error handling agent
            error_agent = agent_registry.get_agent(AgentType.ERROR_HANDLING)
            if error_agent:
                error_context = AgentContext(
                    agent_type=AgentType.ERROR_HANDLING,
                    request_id=request_id,
                    error_info={
                        "error": str(e),
                        "stage": "orchestration",
                        "original_intent": getattr(agent_context, 'user_intent', None)
                    }
                )
                return await error_agent.execute_with_metrics(state, config, error_context)
            else:
                return await self._fallback_response(state, f"System error: {str(e)}")
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Orchestrator can handle all intents as it routes them."""
        return True
    
    def _select_domain_agent(self, intent: str, confidence: float) -> Optional[BaseAgent]:
        """Select the most appropriate domain agent for the intent."""
        
        # Intent to agent mapping
        intent_mapping = {
            "find_service": AgentType.SEARCH,
            "search_deals": AgentType.SEARCH,
            "search_businesses": AgentType.SEARCH,
            "book_service": AgentType.BOOKING,
            "view_bookings": AgentType.BOOKING,
            "booking_management": AgentType.BOOKING,
            "send_email": AgentType.COMMUNICATION,
            "send_whatsapp": AgentType.COMMUNICATION,
            "communication": AgentType.COMMUNICATION,
            "user_profile": AgentType.USER_MANAGEMENT,
            "update_profile": AgentType.USER_MANAGEMENT,
            "user_preferences": AgentType.USER_MANAGEMENT,
        }
        
        # Get agent type for intent
        agent_type = intent_mapping.get(intent)
        if agent_type:
            return agent_registry.get_agent(agent_type)
        
        # Fallback: try to find any agent that can handle this intent
        capable_agents = agent_registry.get_agents_for_intent(intent)
        if capable_agents:
            return capable_agents[0]  # Return first capable agent
        
        # Default to search agent for unknown intents
        return agent_registry.get_agent(AgentType.SEARCH)
    
    async def _fallback_response(self, state: CatchUpState, error_message: str) -> Dict[str, Any]:
        """Generate a fallback response when agents are unavailable."""
        return {
            "messages": [AIMessage(
                content="I apologize, but I'm experiencing some technical difficulties. "
                       "Please try again in a moment or contact support if the issue persists."
            )],
            "orchestrator_error": error_message
        }
    
    def get_workflow_status(self, request_id: str) -> Optional[List[str]]:
        """Get the workflow chain for a specific request."""
        return self.active_workflows.get(request_id)
    
    def cleanup_completed_workflows(self, max_age_seconds: int = 3600):
        """Clean up old workflow tracking data."""
        import time
        current_time = time.time()
        
        # Remove workflows older than max_age_seconds
        expired_requests = [
            request_id for request_id, _ in self.active_workflows.items()
            # Add timestamp tracking if needed
        ]
        
        for request_id in expired_requests:
            self.active_workflows.pop(request_id, None)
            self.conversation_states.pop(request_id, None)


# Create and register the orchestrator agent
orchestrator_agent = OrchestratorAgent()
agent_registry.register_agent(orchestrator_agent)
