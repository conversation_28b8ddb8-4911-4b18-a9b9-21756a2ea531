"""Enhanced tools node with metrics and better error handling."""

from typing import Any, Dict, List, Set
import time
from langchain_core.messages import ToolMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import ToolNode

from agent.state import CatchUpState
from agent.tools.tool_manager import <PERSON>hanced<PERSON><PERSON><PERSON>ana<PERSON>, ToolCategory

class EnhancedCustomToolNode:
    """Enhanced tool node with metrics and intelligent tool loading."""

    def __init__(self):
        self.tool_manager = EnhancedToolManager()
        self._base_tool_node = None
        self._current_categories: Set[ToolCategory] = set()

    async def initialize(self):
        """Initialize the tool manager."""
        await self.tool_manager.initialize()

    async def ainvoke(self, state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Execute tools with enhanced error handling and metrics."""
        messages = state.get("messages", [])
        if not messages:
            return {"messages": []}

        last_message = messages[-1]
        if not isinstance(last_message, AIMessage):
            return {"messages": []}

        # Determine required tool categories from the message
        required_categories = self._determine_required_categories(last_message)
        
        # Load tools for required categories only
        if required_categories != self._current_categories:
            await self._load_tools_for_categories(required_categories)
            self._current_categories = required_categories

        tool_messages = []

        # Handle valid tool calls
        if last_message.tool_calls:
            try:
                start_time = time.time()
                result = await self._base_tool_node.ainvoke(state, config)
                execution_time = time.time() - start_time
                
                # Record metrics for each tool call
                for tool_call in last_message.tool_calls:
                    self.tool_manager.record_tool_execution(
                        tool_call['name'], 
                        execution_time / len(last_message.tool_calls),
                        True
                    )
                
                return result
                
            except Exception as e:
                print(f"Error executing valid tool calls: {e}")
                # Record error metrics
                for tool_call in last_message.tool_calls:
                    self.tool_manager.record_tool_execution(
                        tool_call['name'], 0.0, False
                    )
                
                # Create error messages
                for tool_call in last_message.tool_calls:
                    error_msg = ToolMessage(
                        content=f"Error executing tool {tool_call['name']}: {str(e)}",
                        tool_call_id=tool_call['id']
                    )
                    tool_messages.append(error_msg)

        # Handle invalid tool calls (existing logic enhanced)
        if hasattr(last_message, 'invalid_tool_calls') and last_message.invalid_tool_calls:
            for invalid_call in last_message.invalid_tool_calls:
                tool_name = invalid_call.get('name')
                tool_id = invalid_call.get('id')
                error = invalid_call.get('error', '')

                start_time = time.time()
                success = False

                # Enhanced get_categories handling
                if tool_name == 'get_categories' and 'not valid JSON' in error:
                    try:
                        tools = await self.tool_manager.get_tools_for_categories({ToolCategory.CATEGORIES})
                        if tools:
                            tool = next((t for t in tools if t.name == 'get_categories'), None)
                            if tool:
                                result = await tool.ainvoke({})
                                tool_messages.append(ToolMessage(
                                    content=str(result),
                                    tool_call_id=tool_id
                                ))
                                success = True
                    except Exception as e:
                        print(f"Failed to execute get_categories with empty args: {e}")

                execution_time = time.time() - start_time
                self.tool_manager.record_tool_execution(tool_name, execution_time, success)

                if not success:
                    error_msg = ToolMessage(
                        content=f"Invalid tool call for {tool_name}: {error}. Please check the tool arguments and try again.",
                        tool_call_id=tool_id
                    )
                    tool_messages.append(error_msg)

        return {"messages": tool_messages}

    def _determine_required_categories(self, message: AIMessage) -> Set[ToolCategory]:
        """Determine which tool categories are needed based on the message."""
        categories = set()
        
        # Check valid tool calls
        if message.tool_calls:
            for tool_call in message.tool_calls:
                categories.update(self._get_categories_for_tool(tool_call['name']))
        
        # Check invalid tool calls
        if hasattr(message, 'invalid_tool_calls') and message.invalid_tool_calls:
            for invalid_call in message.invalid_tool_calls:
                tool_name = invalid_call.get('name')
                if tool_name:
                    categories.update(self._get_categories_for_tool(tool_name))
        
        # Default to search if no specific categories found
        if not categories:
            categories.add(ToolCategory.SEARCH)
        
        return categories

    def _get_categories_for_tool(self, tool_name: str) -> Set[ToolCategory]:
        """Get categories for a specific tool name."""
        tool_category_map = {
            'get_categories': {ToolCategory.CATEGORIES},
            'search_deals': {ToolCategory.SEARCH},
            'search_businesses': {ToolCategory.SEARCH},
            'create_booking': {ToolCategory.BOOKING},
            'get_booking': {ToolCategory.BOOKING},
            'update_booking': {ToolCategory.BOOKING},
            'get_user': {ToolCategory.USER_MANAGEMENT},
            'update_user': {ToolCategory.USER_MANAGEMENT},
            'send_email': {ToolCategory.COMMUNICATION},
            'send_whatsapp': {ToolCategory.COMMUNICATION},
        }
        
        return tool_category_map.get(tool_name, {ToolCategory.SEARCH})

    async def _load_tools_for_categories(self, categories: Set[ToolCategory]):
        """Load tools for specific categories."""
        tools = await self.tool_manager.get_tools_for_categories(categories)
        self._base_tool_node = ToolNode(tools)

    def get_tool_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of tool usage metrics."""
        frequently_used = self.tool_manager.get_frequently_used_tools()
        
        return {
            "frequently_used_tools": frequently_used,
            "total_tools_loaded": len(self._current_categories),
            "current_categories": [cat.value for cat in self._current_categories]
        }


# Global enhanced tools node instance
_enhanced_tools_node = None

async def enhanced_tools_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Enhanced tools node wrapper."""
    global _enhanced_tools_node

    if _enhanced_tools_node is None:
        _enhanced_tools_node = EnhancedCustomToolNode()
        await _enhanced_tools_node.initialize()

    return await _enhanced_tools_node.ainvoke(state, config)