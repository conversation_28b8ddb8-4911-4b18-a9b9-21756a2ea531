"""Search Agent for deal discovery, business search, and category exploration."""

from typing import Dict, Any, Optional, List, Set
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext
from agent.state import CatchUpState
from agent.tools.tool_manager import ToolCategory
from shared import create_llm


class SearchAgent(BaseAgent):
    """Agent specialized in search operations for deals, businesses, and categories."""
    
    def __init__(self):
        super().__init__(AgentType.SEARCH, "Search Agent")
        self.required_tool_categories = {
            ToolCategory.SEARCH,
            ToolCategory.CATEGORIES
        }
        self.search_strategies = self._initialize_search_strategies()
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Process search requests with intelligent filtering and ranking."""
        
        # Load tools if not already loaded
        if not self._tools:
            await self.load_tools()
        
        # Extract search parameters from context and state
        search_params = self._extract_search_parameters(state, context)
        
        # Determine search strategy
        strategy = self._select_search_strategy(search_params)
        
        # Execute search
        search_results = await self._execute_search(search_params, strategy, state, config)
        
        # Rank and filter results
        filtered_results = self._rank_and_filter_results(search_results, search_params)
        
        # Generate response
        response = await self._generate_search_response(filtered_results, search_params, state, config)
        
        return {
            "messages": [response],
            "search_results": filtered_results,
            "search_metadata": {
                "strategy_used": strategy,
                "total_results": len(search_results.get("results", [])),
                "filtered_results": len(filtered_results.get("results", [])),
                "search_params": search_params
            }
        }
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Check if this agent can handle search-related intents."""
        search_intents = [
            "find_service", "search_deals", "search_businesses", 
            "browse_categories", "discover_offers", "location_search"
        ]
        return intent in search_intents
    
    def _extract_search_parameters(self, state: CatchUpState, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Extract search parameters from state and context."""
        
        # Get user context
        user_context = state.get("user_context", {})
        location = user_context.get("location", {})
        
        # Get entities from intent analysis
        entities = {}
        if context and context.metadata:
            entities = context.metadata.get("entities", {})
        
        # Extract from last message
        messages = state.get("messages", [])
        search_query = ""
        if messages:
            last_message = messages[-1]
            search_query = last_message.content if hasattr(last_message, 'content') else str(last_message)
        
        return {
            "query": search_query,
            "location": {
                "latitude": location.get("latitude"),
                "longitude": location.get("longitude")
            },
            "entities": entities,
            "user_id": user_context.get("user_id"),
            "categories": entities.get("categories", []),
            "time_preferences": entities.get("time", []),
            "location_mentions": entities.get("location", [])
        }
    
    def _select_search_strategy(self, search_params: Dict[str, Any]) -> str:
        """Select the most appropriate search strategy."""
        
        # Category-specific search
        if search_params.get("categories"):
            return "category_focused"
        
        # Location-specific search
        if search_params.get("location_mentions") or search_params.get("location", {}).get("latitude"):
            return "location_based"
        
        # Time-sensitive search
        if search_params.get("time_preferences"):
            return "time_filtered"
        
        # General search
        return "general_search"
    
    async def _execute_search(
        self, 
        search_params: Dict[str, Any], 
        strategy: str, 
        state: CatchUpState, 
        config: RunnableConfig
    ) -> Dict[str, Any]:
        """Execute search using the selected strategy."""
        
        results = {"results": [], "metadata": {}}
        
        try:
            if strategy == "category_focused":
                results = await self._category_focused_search(search_params, state, config)
            elif strategy == "location_based":
                results = await self._location_based_search(search_params, state, config)
            elif strategy == "time_filtered":
                results = await self._time_filtered_search(search_params, state, config)
            else:
                results = await self._general_search(search_params, state, config)
                
        except Exception as e:
            print(f"Search execution error: {e}")
            results = {
                "results": [],
                "error": str(e),
                "metadata": {"strategy": strategy}
            }
        
        return results
    
    async def _category_focused_search(self, search_params: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Execute category-focused search."""
        
        # First get categories to validate
        categories_tool = self._get_tool_by_name("get_categories")
        if categories_tool:
            try:
                categories_result = await categories_tool.ainvoke({})
                available_categories = categories_result if isinstance(categories_result, list) else []
            except Exception as e:
                print(f"Error getting categories: {e}")
                available_categories = []
        else:
            available_categories = []
        
        # Search for deals in specified categories
        search_tool = self._get_tool_by_name("search_deals")
        if search_tool:
            try:
                search_result = await search_tool.ainvoke({
                    "query": search_params.get("query", ""),
                    "category": search_params.get("categories", [None])[0],  # Use first category
                    "latitude": search_params.get("location", {}).get("latitude"),
                    "longitude": search_params.get("location", {}).get("longitude")
                })
                
                return {
                    "results": search_result if isinstance(search_result, list) else [search_result],
                    "metadata": {
                        "search_type": "category_focused",
                        "categories_searched": search_params.get("categories", []),
                        "available_categories": len(available_categories)
                    }
                }
            except Exception as e:
                print(f"Error in category search: {e}")
        
        return {"results": [], "metadata": {"error": "Category search failed"}}
    
    async def _location_based_search(self, search_params: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Execute location-based search."""
        
        # Search businesses by location
        business_tool = self._get_tool_by_name("search_businesses")
        deals_tool = self._get_tool_by_name("search_deals")
        
        results = []
        
        # Search businesses
        if business_tool:
            try:
                business_result = await business_tool.ainvoke({
                    "query": search_params.get("query", ""),
                    "latitude": search_params.get("location", {}).get("latitude"),
                    "longitude": search_params.get("location", {}).get("longitude")
                })
                if isinstance(business_result, list):
                    results.extend(business_result)
                else:
                    results.append(business_result)
            except Exception as e:
                print(f"Error in business search: {e}")
        
        # Search deals by location
        if deals_tool:
            try:
                deals_result = await deals_tool.ainvoke({
                    "query": search_params.get("query", ""),
                    "latitude": search_params.get("location", {}).get("latitude"),
                    "longitude": search_params.get("location", {}).get("longitude")
                })
                if isinstance(deals_result, list):
                    results.extend(deals_result)
                else:
                    results.append(deals_result)
            except Exception as e:
                print(f"Error in deals search: {e}")
        
        return {
            "results": results,
            "metadata": {
                "search_type": "location_based",
                "location": search_params.get("location", {}),
                "location_mentions": search_params.get("location_mentions", [])
            }
        }
    
    async def _time_filtered_search(self, search_params: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Execute time-filtered search."""
        
        # Get all deals first, then filter by time
        deals_tool = self._get_tool_by_name("search_deals")
        if deals_tool:
            try:
                deals_result = await deals_tool.ainvoke({
                    "query": search_params.get("query", ""),
                    "latitude": search_params.get("location", {}).get("latitude"),
                    "longitude": search_params.get("location", {}).get("longitude")
                })
                
                # Filter results by time preferences
                filtered_results = self._filter_by_time_preferences(
                    deals_result if isinstance(deals_result, list) else [deals_result],
                    search_params.get("time_preferences", [])
                )
                
                return {
                    "results": filtered_results,
                    "metadata": {
                        "search_type": "time_filtered",
                        "time_preferences": search_params.get("time_preferences", []),
                        "original_count": len(deals_result) if isinstance(deals_result, list) else 1,
                        "filtered_count": len(filtered_results)
                    }
                }
            except Exception as e:
                print(f"Error in time-filtered search: {e}")
        
        return {"results": [], "metadata": {"error": "Time-filtered search failed"}}
    
    async def _general_search(self, search_params: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Execute general search."""
        
        # Try both deals and businesses search
        deals_tool = self._get_tool_by_name("search_deals")
        business_tool = self._get_tool_by_name("search_businesses")
        
        results = []
        
        # Search deals
        if deals_tool:
            try:
                deals_result = await deals_tool.ainvoke({
                    "query": search_params.get("query", ""),
                    "latitude": search_params.get("location", {}).get("latitude"),
                    "longitude": search_params.get("location", {}).get("longitude")
                })
                if isinstance(deals_result, list):
                    results.extend(deals_result)
                elif deals_result:
                    results.append(deals_result)
            except Exception as e:
                print(f"Error in general deals search: {e}")
        
        # Search businesses
        if business_tool:
            try:
                business_result = await business_tool.ainvoke({
                    "query": search_params.get("query", ""),
                    "latitude": search_params.get("location", {}).get("latitude"),
                    "longitude": search_params.get("location", {}).get("longitude")
                })
                if isinstance(business_result, list):
                    results.extend(business_result)
                elif business_result:
                    results.append(business_result)
            except Exception as e:
                print(f"Error in general business search: {e}")
        
        return {
            "results": results,
            "metadata": {
                "search_type": "general",
                "query": search_params.get("query", "")
            }
        }
    
    def _rank_and_filter_results(self, search_results: Dict[str, Any], search_params: Dict[str, Any]) -> Dict[str, Any]:
        """Rank and filter search results based on relevance."""
        
        results = search_results.get("results", [])
        if not results:
            return search_results
        
        # Apply ranking algorithm
        ranked_results = self._apply_ranking_algorithm(results, search_params)
        
        # Apply filters
        filtered_results = self._apply_filters(ranked_results, search_params)
        
        return {
            "results": filtered_results,
            "metadata": {
                **search_results.get("metadata", {}),
                "ranking_applied": True,
                "original_count": len(results),
                "final_count": len(filtered_results)
            }
        }
    
    def _apply_ranking_algorithm(self, results: List[Dict], search_params: Dict[str, Any]) -> List[Dict]:
        """Apply ranking algorithm to sort results by relevance."""
        
        # Simple ranking based on multiple factors
        def calculate_score(result):
            score = 0
            
            # Distance score (if location available)
            if search_params.get("location", {}).get("latitude"):
                # Would calculate distance-based score in real implementation
                score += 10
            
            # Category match score
            if search_params.get("categories"):
                # Would check category match in real implementation
                score += 15
            
            # Availability score
            if result.get("available", True):
                score += 20
            
            # Discount score
            discount = result.get("discount_percentage", 0)
            if discount:
                score += min(discount / 10, 10)  # Max 10 points for discount
            
            return score
        
        # Sort by score (descending)
        try:
            return sorted(results, key=calculate_score, reverse=True)
        except Exception as e:
            print(f"Ranking error: {e}")
            return results
    
    def _apply_filters(self, results: List[Dict], search_params: Dict[str, Any]) -> List[Dict]:
        """Apply filters to remove irrelevant results."""
        
        filtered = results
        
        # Filter by availability
        filtered = [r for r in filtered if r.get("available", True)]
        
        # Filter by location if specified
        location_mentions = search_params.get("location_mentions", [])
        if location_mentions:
            # Would implement location filtering in real implementation
            pass
        
        # Limit results to top 10
        return filtered[:10]
    
    def _filter_by_time_preferences(self, results: List[Dict], time_preferences: List[str]) -> List[Dict]:
        """Filter results by time preferences."""
        
        if not time_preferences:
            return results
        
        # Simple time filtering - would be more sophisticated in real implementation
        filtered = []
        for result in results:
            # Check if result matches time preferences
            # This is a simplified implementation
            if any(pref in str(result).lower() for pref in time_preferences):
                filtered.append(result)
            elif not time_preferences:  # If no specific time mentioned, include all
                filtered.append(result)
        
        return filtered or results  # Return original if no matches
    
    async def _generate_search_response(
        self, 
        search_results: Dict[str, Any], 
        search_params: Dict[str, Any], 
        state: CatchUpState, 
        config: RunnableConfig
    ) -> AIMessage:
        """Generate a natural language response for search results."""
        
        results = search_results.get("results", [])
        
        if not results:
            return AIMessage(content="I couldn't find any results matching your search criteria. Try adjusting your search terms or location.")
        
        # Create response based on results
        if len(results) == 1:
            response = f"I found 1 result for your search:\n\n"
        else:
            response = f"I found {len(results)} results for your search:\n\n"
        
        # Add top results to response
        for i, result in enumerate(results[:3], 1):  # Show top 3
            name = result.get("name", "Unknown")
            description = result.get("description", "")
            discount = result.get("discount_percentage", 0)
            
            response += f"{i}. **{name}**\n"
            if description:
                response += f"   {description[:100]}{'...' if len(description) > 100 else ''}\n"
            if discount:
                response += f"   💰 {discount}% discount\n"
            response += "\n"
        
        if len(results) > 3:
            response += f"... and {len(results) - 3} more results available.\n"
        
        return AIMessage(content=response)
    
    def _get_tool_by_name(self, tool_name: str):
        """Get a tool by name from loaded tools."""
        if not self._tools:
            return None
        
        for tool in self._tools:
            if hasattr(tool, 'name') and tool.name == tool_name:
                return tool
        return None
    
    def _initialize_search_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize search strategy configurations."""
        return {
            "category_focused": {
                "priority": ["category_match", "availability", "discount"],
                "filters": ["category", "availability"],
                "max_results": 10
            },
            "location_based": {
                "priority": ["distance", "rating", "availability"],
                "filters": ["location", "availability"],
                "max_results": 15
            },
            "time_filtered": {
                "priority": ["time_match", "availability", "discount"],
                "filters": ["time", "availability"],
                "max_results": 8
            },
            "general_search": {
                "priority": ["relevance", "availability", "rating"],
                "filters": ["availability"],
                "max_results": 12
            }
        }
