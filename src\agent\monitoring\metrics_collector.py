"""Metrics collection and monitoring for the agentic system."""

import time
import asyncio
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import json

from agent.agents.base_agent import AgentType, AgentStatus


class MetricType(Enum):
    """Types of metrics collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricPoint:
    """A single metric data point."""
    timestamp: float
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class Metric:
    """Metric definition and data."""
    name: str
    metric_type: MetricType
    description: str
    unit: str = ""
    data_points: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    def add_point(self, value: float, labels: Optional[Dict[str, str]] = None):
        """Add a data point to the metric."""
        point = MetricPoint(
            timestamp=time.time(),
            value=value,
            labels=labels or {}
        )
        self.data_points.append(point)


class MetricsCollector:
    """Central metrics collection system."""
    
    def __init__(self):
        self.metrics: Dict[str, Metric] = {}
        self.agent_metrics: Dict[AgentType, Dict[str, Any]] = defaultdict(dict)
        self.system_metrics: Dict[str, Any] = {}
        self.collection_interval = 10.0  # seconds
        self._collecting = False
        self._collection_task: Optional[asyncio.Task] = None
        
        # Initialize standard metrics
        self._initialize_standard_metrics()
    
    def _initialize_standard_metrics(self):
        """Initialize standard system metrics."""
        
        # Agent-level metrics
        self.register_metric(
            "agent_requests_total",
            MetricType.COUNTER,
            "Total number of requests processed by each agent"
        )
        
        self.register_metric(
            "agent_request_duration_seconds",
            MetricType.HISTOGRAM,
            "Request processing duration in seconds",
            "seconds"
        )
        
        self.register_metric(
            "agent_errors_total",
            MetricType.COUNTER,
            "Total number of errors by agent"
        )
        
        self.register_metric(
            "agent_status",
            MetricType.GAUGE,
            "Current status of each agent (0=idle, 1=processing, 2=error, 3=completed)"
        )
        
        # System-level metrics
        self.register_metric(
            "system_active_conversations",
            MetricType.GAUGE,
            "Number of active conversations"
        )
        
        self.register_metric(
            "system_total_agents",
            MetricType.GAUGE,
            "Total number of registered agents"
        )
        
        self.register_metric(
            "system_healthy_agents",
            MetricType.GAUGE,
            "Number of healthy agents"
        )
        
        self.register_metric(
            "system_response_time_p95",
            MetricType.GAUGE,
            "95th percentile response time",
            "seconds"
        )
        
        # Tool usage metrics
        self.register_metric(
            "tool_calls_total",
            MetricType.COUNTER,
            "Total number of tool calls"
        )
        
        self.register_metric(
            "tool_call_duration_seconds",
            MetricType.HISTOGRAM,
            "Tool call duration in seconds",
            "seconds"
        )
        
        self.register_metric(
            "tool_errors_total",
            MetricType.COUNTER,
            "Total number of tool errors"
        )
    
    def register_metric(self, name: str, metric_type: MetricType, description: str, unit: str = ""):
        """Register a new metric."""
        self.metrics[name] = Metric(
            name=name,
            metric_type=metric_type,
            description=description,
            unit=unit
        )
    
    def increment_counter(self, name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """Increment a counter metric."""
        if name in self.metrics and self.metrics[name].metric_type == MetricType.COUNTER:
            # For counters, we add to the last value or start from 0
            last_value = 0.0
            if self.metrics[name].data_points:
                last_value = self.metrics[name].data_points[-1].value
            self.metrics[name].add_point(last_value + value, labels)
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Set a gauge metric value."""
        if name in self.metrics and self.metrics[name].metric_type == MetricType.GAUGE:
            self.metrics[name].add_point(value, labels)
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record a histogram value."""
        if name in self.metrics and self.metrics[name].metric_type == MetricType.HISTOGRAM:
            self.metrics[name].add_point(value, labels)
    
    def time_operation(self, name: str, labels: Optional[Dict[str, str]] = None):
        """Context manager for timing operations."""
        return TimerContext(self, name, labels)
    
    async def start_collection(self):
        """Start automatic metrics collection."""
        if not self._collecting:
            self._collecting = True
            self._collection_task = asyncio.create_task(self._collect_metrics_loop())
    
    async def stop_collection(self):
        """Stop automatic metrics collection."""
        self._collecting = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
    
    async def _collect_metrics_loop(self):
        """Background loop for collecting metrics."""
        while self._collecting:
            try:
                await self._collect_system_metrics()
                await self._collect_agent_metrics()
                await asyncio.sleep(self.collection_interval)
            except Exception as e:
                print(f"Error in metrics collection: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def _collect_system_metrics(self):
        """Collect system-level metrics."""
        from agent.agents import agent_registry
        
        # Count agents by status
        agents = agent_registry.get_all_agents()
        total_agents = len(agents)
        healthy_agents = sum(1 for agent in agents if agent.status != AgentStatus.ERROR)
        
        self.set_gauge("system_total_agents", total_agents)
        self.set_gauge("system_healthy_agents", healthy_agents)
        
        # Calculate system response time percentiles
        response_times = []
        for agent in agents:
            if agent.metrics.avg_execution_time > 0:
                response_times.append(agent.metrics.avg_execution_time)
        
        if response_times:
            response_times.sort()
            p95_index = int(0.95 * len(response_times))
            p95_time = response_times[p95_index] if p95_index < len(response_times) else response_times[-1]
            self.set_gauge("system_response_time_p95", p95_time)
    
    async def _collect_agent_metrics(self):
        """Collect agent-specific metrics."""
        from agent.agents import agent_registry
        
        for agent in agent_registry.get_all_agents():
            agent_labels = {"agent_type": agent.agent_type.value, "agent_name": agent.name}
            
            # Agent status
            status_value = {
                AgentStatus.IDLE: 0,
                AgentStatus.PROCESSING: 1,
                AgentStatus.ERROR: 2,
                AgentStatus.COMPLETED: 3
            }.get(agent.status, 0)
            
            self.set_gauge("agent_status", status_value, agent_labels)
            
            # Request metrics
            self.set_gauge("agent_requests_total", agent.metrics.total_requests, agent_labels)
            self.set_gauge("agent_errors_total", agent.metrics.failed_requests, agent_labels)
            
            # Response time
            if agent.metrics.avg_execution_time > 0:
                self.record_histogram("agent_request_duration_seconds", agent.metrics.avg_execution_time, agent_labels)
    
    def get_metric_summary(self, name: str) -> Dict[str, Any]:
        """Get summary statistics for a metric."""
        if name not in self.metrics:
            return {}
        
        metric = self.metrics[name]
        if not metric.data_points:
            return {"name": name, "type": metric.metric_type.value, "no_data": True}
        
        values = [point.value for point in metric.data_points]
        
        summary = {
            "name": name,
            "type": metric.metric_type.value,
            "description": metric.description,
            "unit": metric.unit,
            "data_points": len(values),
            "latest_value": values[-1],
            "latest_timestamp": metric.data_points[-1].timestamp
        }
        
        if metric.metric_type in [MetricType.HISTOGRAM, MetricType.TIMER]:
            summary.update({
                "min": min(values),
                "max": max(values),
                "avg": sum(values) / len(values),
                "count": len(values)
            })
            
            # Calculate percentiles
            sorted_values = sorted(values)
            summary.update({
                "p50": sorted_values[int(0.5 * len(sorted_values))],
                "p95": sorted_values[int(0.95 * len(sorted_values))],
                "p99": sorted_values[int(0.99 * len(sorted_values))]
            })
        
        return summary
    
    def get_all_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all metrics."""
        return {
            "metrics": {name: self.get_metric_summary(name) for name in self.metrics.keys()},
            "collection_interval": self.collection_interval,
            "collecting": self._collecting,
            "timestamp": time.time()
        }
    
    def export_prometheus_format(self) -> str:
        """Export metrics in Prometheus format."""
        lines = []
        
        for metric in self.metrics.values():
            if not metric.data_points:
                continue
            
            # Add help and type comments
            lines.append(f"# HELP {metric.name} {metric.description}")
            lines.append(f"# TYPE {metric.name} {metric.metric_type.value}")
            
            # Add data points
            for point in metric.data_points:
                labels_str = ""
                if point.labels:
                    label_pairs = [f'{k}="{v}"' for k, v in point.labels.items()]
                    labels_str = "{" + ",".join(label_pairs) + "}"
                
                lines.append(f"{metric.name}{labels_str} {point.value} {int(point.timestamp * 1000)}")
            
            lines.append("")  # Empty line between metrics
        
        return "\n".join(lines)
    
    def export_json_format(self) -> str:
        """Export metrics in JSON format."""
        data = {
            "timestamp": time.time(),
            "metrics": {}
        }
        
        for name, metric in self.metrics.items():
            metric_data = {
                "type": metric.metric_type.value,
                "description": metric.description,
                "unit": metric.unit,
                "data_points": []
            }
            
            for point in metric.data_points:
                metric_data["data_points"].append({
                    "timestamp": point.timestamp,
                    "value": point.value,
                    "labels": point.labels
                })
            
            data["metrics"][name] = metric_data
        
        return json.dumps(data, indent=2)
    
    def identify_bottlenecks(self) -> List[Dict[str, Any]]:
        """Identify system bottlenecks based on metrics."""
        bottlenecks = []
        
        # Check for slow agents
        for name, metric in self.metrics.items():
            if name == "agent_request_duration_seconds" and metric.data_points:
                values = [point.value for point in metric.data_points]
                avg_time = sum(values) / len(values)
                
                if avg_time > 5.0:  # More than 5 seconds average
                    bottlenecks.append({
                        "type": "slow_agent",
                        "metric": name,
                        "avg_response_time": avg_time,
                        "severity": "high" if avg_time > 10.0 else "medium"
                    })
        
        # Check for high error rates
        for name, metric in self.metrics.items():
            if name == "agent_errors_total" and metric.data_points:
                latest_errors = metric.data_points[-1].value if metric.data_points else 0
                if latest_errors > 10:  # More than 10 errors
                    bottlenecks.append({
                        "type": "high_error_rate",
                        "metric": name,
                        "error_count": latest_errors,
                        "severity": "high" if latest_errors > 50 else "medium"
                    })
        
        # Check for unhealthy agents
        healthy_metric = self.metrics.get("system_healthy_agents")
        total_metric = self.metrics.get("system_total_agents")
        
        if healthy_metric and total_metric and healthy_metric.data_points and total_metric.data_points:
            healthy_count = healthy_metric.data_points[-1].value
            total_count = total_metric.data_points[-1].value
            
            if total_count > 0 and healthy_count / total_count < 0.8:  # Less than 80% healthy
                bottlenecks.append({
                    "type": "unhealthy_agents",
                    "healthy_ratio": healthy_count / total_count,
                    "healthy_count": healthy_count,
                    "total_count": total_count,
                    "severity": "high"
                })
        
        return bottlenecks


class TimerContext:
    """Context manager for timing operations."""
    
    def __init__(self, collector: MetricsCollector, metric_name: str, labels: Optional[Dict[str, str]] = None):
        self.collector = collector
        self.metric_name = metric_name
        self.labels = labels
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.collector.record_histogram(self.metric_name, duration, self.labels)


# Global metrics collector instance
metrics_collector = MetricsCollector()
