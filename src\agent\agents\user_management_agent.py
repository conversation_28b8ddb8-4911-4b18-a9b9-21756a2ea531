"""User Management Agent for user profile and preferences management."""

from typing import Dict, Any, Optional, List
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext
from agent.state import CatchUpState
from agent.tools.tool_manager import ToolCategory


class UserManagementAgent(BaseAgent):
    """Agent specialized in user profile management and personalization."""
    
    def __init__(self):
        super().__init__(AgentType.USER_MANAGEMENT, "User Management Agent")
        self.required_tool_categories = {
            ToolCategory.USER_MANAGEMENT
        }
        self.profile_fields = self._initialize_profile_fields()
        self.privacy_settings = self._initialize_privacy_settings()
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Process user management requests with profile management and personalization."""
        
        # Load tools if not already loaded
        if not self._tools:
            await self.load_tools()
        
        # Determine user management operation
        operation = self._determine_user_operation(state, context)
        
        # Execute user management operation
        result = await self._execute_user_operation(operation, state, config, context)
        
        return result
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Check if this agent can handle user management intents."""
        user_management_intents = [
            "user_profile", "update_profile", "user_preferences", 
            "view_profile", "manage_account", "privacy_settings",
            "personal_info", "account_settings"
        ]
        return intent in user_management_intents
    
    def _determine_user_operation(self, state: CatchUpState, context: Optional[AgentContext]) -> str:
        """Determine the type of user management operation requested."""
        
        messages = state.get("messages", [])
        if not messages:
            return "view_profile"
        
        last_message = messages[-1]
        message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
        message_lower = message_content.lower()
        
        # Check for specific user operations
        if any(word in message_lower for word in ["aggiorna", "update", "modifica", "change", "edit"]):
            return "update_profile"
        elif any(word in message_lower for word in ["preferenze", "preferences", "settings", "impostazioni"]):
            return "manage_preferences"
        elif any(word in message_lower for word in ["privacy", "sicurezza", "security", "private"]):
            return "privacy_settings"
        elif any(word in message_lower for word in ["profilo", "profile", "account", "info", "dati"]):
            return "view_profile"
        elif any(word in message_lower for word in ["elimina", "delete", "cancella", "remove"]):
            return "delete_account"
        else:
            return "view_profile"  # Default operation
    
    async def _execute_user_operation(
        self, 
        operation: str, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext]
    ) -> Dict[str, Any]:
        """Execute the specific user management operation."""
        
        try:
            if operation == "view_profile":
                return await self._view_user_profile(state, config, context)
            elif operation == "update_profile":
                return await self._update_user_profile(state, config, context)
            elif operation == "manage_preferences":
                return await self._manage_user_preferences(state, config, context)
            elif operation == "privacy_settings":
                return await self._manage_privacy_settings(state, config, context)
            elif operation == "delete_account":
                return await self._handle_account_deletion(state, config, context)
            else:
                return {"messages": [AIMessage(content="I'm not sure what user management operation you'd like to perform. Could you please clarify?")]}
                
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I encountered an error while processing your request: {str(e)}")],
                "user_management_error": str(e)
            }
    
    async def _view_user_profile(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """View user profile information."""
        
        user_context = state.get("user_context", {})
        user_id = user_context.get("user_id")
        
        if not user_id:
            return {
                "messages": [AIMessage(content="I need to identify you to show your profile. Please make sure you're logged in.")],
                "missing_user_id": True
            }
        
        # Get user details
        get_user_tool = self._get_tool_by_name("get_user")
        if not get_user_tool:
            return {"messages": [AIMessage(content="User service is currently unavailable.")]}
        
        try:
            user_details = await get_user_tool.ainvoke({"user_id": user_id})
            
            profile_message = self._format_user_profile(user_details, user_context)
            
            return {
                "messages": [AIMessage(content=profile_message)],
                "user_profile": user_details
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I couldn't retrieve your profile: {str(e)}")],
                "profile_error": str(e)
            }
    
    async def _update_user_profile(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Update user profile information."""
        
        user_context = state.get("user_context", {})
        user_id = user_context.get("user_id")
        
        if not user_id:
            return {
                "messages": [AIMessage(content="I need to identify you to update your profile. Please make sure you're logged in.")],
                "missing_user_id": True
            }
        
        # Extract update parameters
        update_params = self._extract_update_parameters(state, context)
        
        if not update_params:
            return {
                "messages": [AIMessage(content="What would you like to update in your profile? (name, email, phone, preferences, etc.)")],
                "needs_update_details": True
            }
        
        # Validate update parameters
        validation_result = self._validate_update_parameters(update_params)
        if not validation_result["valid"]:
            return {
                "messages": [AIMessage(content=validation_result["message"])],
                "validation_error": validation_result["message"]
            }
        
        # Update user profile
        update_user_tool = self._get_tool_by_name("update_user")
        if not update_user_tool:
            return {"messages": [AIMessage(content="User update service is currently unavailable.")]}
        
        try:
            update_result = await update_user_tool.ainvoke({
                "user_id": user_id,
                **update_params
            })
            
            success_message = self._generate_update_confirmation(update_params)
            
            return {
                "messages": [AIMessage(content=success_message)],
                "profile_updated": True,
                "update_result": update_result
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I couldn't update your profile: {str(e)}")],
                "update_error": str(e)
            }
    
    async def _manage_user_preferences(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Manage user preferences and settings."""
        
        user_context = state.get("user_context", {})
        user_id = user_context.get("user_id")
        
        if not user_id:
            return {
                "messages": [AIMessage(content="I need to identify you to manage your preferences.")],
                "missing_user_id": True
            }
        
        # Extract preference changes
        preference_changes = self._extract_preference_changes(state, context)
        
        if not preference_changes:
            # Show current preferences
            current_preferences = await self._get_current_preferences(user_id)
            preferences_message = self._format_preferences_display(current_preferences)
            
            return {
                "messages": [AIMessage(content=preferences_message)],
                "current_preferences": current_preferences
            }
        
        # Update preferences
        try:
            updated_preferences = await self._update_user_preferences(user_id, preference_changes)
            
            success_message = f"✅ Your preferences have been updated successfully!\n\n"
            success_message += self._format_preference_changes(preference_changes)
            
            return {
                "messages": [AIMessage(content=success_message)],
                "preferences_updated": True,
                "updated_preferences": updated_preferences
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I couldn't update your preferences: {str(e)}")],
                "preferences_error": str(e)
            }
    
    async def _manage_privacy_settings(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Manage user privacy settings."""
        
        privacy_message = """🔒 **Privacy Settings**

Here are your privacy options:

**Communication Preferences:**
• Email notifications: Enabled
• WhatsApp messages: Enabled  
• SMS notifications: Disabled

**Data Sharing:**
• Location sharing: Enabled for search
• Usage analytics: Enabled (anonymous)
• Marketing communications: Enabled

**Account Security:**
• Two-factor authentication: Recommended
• Login notifications: Enabled

To change any of these settings, please specify what you'd like to modify.
For account deletion or major privacy changes, please contact our support team."""
        
        return {
            "messages": [AIMessage(content=privacy_message)],
            "privacy_settings_displayed": True
        }
    
    async def _handle_account_deletion(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Handle account deletion requests."""
        
        deletion_message = """⚠️ **Account Deletion Request**

I understand you want to delete your account. This is a serious action that cannot be undone.

**What will be deleted:**
• Your profile and personal information
• Your booking history
• Your preferences and settings
• Your communication history

**What will be retained (as required by law):**
• Transaction records (for accounting purposes)
• Legal compliance data (anonymized)

**Before proceeding:**
• Consider if you just want to update your preferences instead
• Make sure you've downloaded any data you want to keep
• Cancel any upcoming bookings

To proceed with account deletion, please contact our support team directly through the app <NAME_EMAIL>. They will guide you through the secure deletion process and verify your identity.

Is there anything else I can help you with instead?"""
        
        return {
            "messages": [AIMessage(content=deletion_message)],
            "account_deletion_requested": True,
            "requires_human_support": True
        }
    
    def _extract_update_parameters(self, state: CatchUpState, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Extract update parameters from messages."""
        
        messages = state.get("messages", [])
        if not messages:
            return {}
        
        last_message = messages[-1]
        message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
        message_lower = message_content.lower()
        
        updates = {}
        
        # Extract email updates
        import re
        email_match = re.search(r'email[:\s]*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', message_content)
        if email_match:
            updates["email"] = email_match.group(1)
        
        # Extract phone updates
        phone_match = re.search(r'phone[:\s]*([+]?[\d\s\-\(\)]{10,})', message_content)
        if phone_match:
            updates["phone"] = phone_match.group(1).strip()
        
        # Extract name updates
        if "name" in message_lower or "nome" in message_lower:
            # Simple name extraction - would be more sophisticated in real implementation
            name_match = re.search(r'name[:\s]*([a-zA-Z\s]+)', message_content, re.IGNORECASE)
            if name_match:
                updates["name"] = name_match.group(1).strip()
        
        return updates
    
    def _validate_update_parameters(self, update_params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate update parameters."""
        
        # Validate email format
        if "email" in update_params:
            email = update_params["email"]
            import re
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                return {"valid": False, "message": "Please provide a valid email address."}
        
        # Validate phone format
        if "phone" in update_params:
            phone = update_params["phone"]
            # Remove spaces and special characters for validation
            clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
            if not clean_phone.isdigit() or len(clean_phone) < 10:
                return {"valid": False, "message": "Please provide a valid phone number."}
        
        # Validate name
        if "name" in update_params:
            name = update_params["name"]
            if len(name.strip()) < 2:
                return {"valid": False, "message": "Please provide a valid name (at least 2 characters)."}
        
        return {"valid": True, "message": "All parameters are valid"}
    
    def _extract_preference_changes(self, state: CatchUpState, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Extract preference changes from messages."""
        
        messages = state.get("messages", [])
        if not messages:
            return {}
        
        last_message = messages[-1]
        message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
        message_lower = message_content.lower()
        
        preferences = {}
        
        # Communication preferences
        if "email" in message_lower:
            if any(word in message_lower for word in ["disable", "off", "stop", "no"]):
                preferences["email_notifications"] = False
            elif any(word in message_lower for word in ["enable", "on", "yes", "allow"]):
                preferences["email_notifications"] = True
        
        if "whatsapp" in message_lower:
            if any(word in message_lower for word in ["disable", "off", "stop", "no"]):
                preferences["whatsapp_notifications"] = False
            elif any(word in message_lower for word in ["enable", "on", "yes", "allow"]):
                preferences["whatsapp_notifications"] = True
        
        # Location preferences
        if "location" in message_lower or "posizione" in message_lower:
            if any(word in message_lower for word in ["disable", "off", "stop", "no"]):
                preferences["location_sharing"] = False
            elif any(word in message_lower for word in ["enable", "on", "yes", "allow"]):
                preferences["location_sharing"] = True
        
        return preferences
    
    async def _get_current_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get current user preferences."""
        
        # In real implementation, would fetch from user preferences service
        # For now, return default preferences
        return {
            "email_notifications": True,
            "whatsapp_notifications": True,
            "sms_notifications": False,
            "location_sharing": True,
            "marketing_communications": True,
            "language": "it",
            "currency": "EUR"
        }
    
    async def _update_user_preferences(self, user_id: str, preference_changes: Dict[str, Any]) -> Dict[str, Any]:
        """Update user preferences."""
        
        # In real implementation, would call preference update service
        # For now, simulate successful update
        current_prefs = await self._get_current_preferences(user_id)
        current_prefs.update(preference_changes)
        
        return current_prefs
    
    def _format_user_profile(self, user_details: Any, user_context: Dict[str, Any]) -> str:
        """Format user profile for display."""
        
        profile_message = "👤 **Your Profile**\n\n"
        
        if isinstance(user_details, dict):
            name = user_details.get("name", user_context.get("name", "Not provided"))
            email = user_details.get("email", user_context.get("email_address", "Not provided"))
            phone = user_details.get("phone", user_context.get("phone_number", "Not provided"))
            
            profile_message += f"**Name:** {name}\n"
            profile_message += f"**Email:** {email}\n"
            profile_message += f"**Phone:** {phone}\n"
            
            if user_details.get("location"):
                profile_message += f"**Location:** {user_details['location']}\n"
            
            profile_message += f"\n**Member since:** {user_details.get('created_date', 'Unknown')}\n"
            profile_message += f"**Total bookings:** {user_details.get('total_bookings', 0)}\n"
        else:
            profile_message += "Profile information is available. "
        
        profile_message += "\n💡 To update any information, just tell me what you'd like to change!"
        
        return profile_message
    
    def _generate_update_confirmation(self, update_params: Dict[str, Any]) -> str:
        """Generate confirmation message for profile updates."""
        
        confirmation = "✅ **Profile Updated Successfully!**\n\n"
        confirmation += "The following information has been updated:\n"
        
        for field, value in update_params.items():
            field_name = field.replace("_", " ").title()
            if field == "email":
                confirmation += f"• **{field_name}:** {value}\n"
            elif field == "phone":
                confirmation += f"• **{field_name}:** {value}\n"
            elif field == "name":
                confirmation += f"• **{field_name}:** {value}\n"
            else:
                confirmation += f"• **{field_name}:** Updated\n"
        
        confirmation += "\n📧 You may receive a confirmation email for security purposes."
        
        return confirmation
    
    def _format_preferences_display(self, preferences: Dict[str, Any]) -> str:
        """Format preferences for display."""
        
        prefs_message = "⚙️ **Your Preferences**\n\n"
        
        prefs_message += "**Communication:**\n"
        prefs_message += f"• Email notifications: {'✅ Enabled' if preferences.get('email_notifications') else '❌ Disabled'}\n"
        prefs_message += f"• WhatsApp messages: {'✅ Enabled' if preferences.get('whatsapp_notifications') else '❌ Disabled'}\n"
        prefs_message += f"• SMS notifications: {'✅ Enabled' if preferences.get('sms_notifications') else '❌ Disabled'}\n\n"
        
        prefs_message += "**Privacy:**\n"
        prefs_message += f"• Location sharing: {'✅ Enabled' if preferences.get('location_sharing') else '❌ Disabled'}\n"
        prefs_message += f"• Marketing communications: {'✅ Enabled' if preferences.get('marketing_communications') else '❌ Disabled'}\n\n"
        
        prefs_message += "**Regional:**\n"
        prefs_message += f"• Language: {preferences.get('language', 'Not set')}\n"
        prefs_message += f"• Currency: {preferences.get('currency', 'Not set')}\n\n"
        
        prefs_message += "💡 To change any preference, just tell me what you'd like to modify!"
        
        return prefs_message
    
    def _format_preference_changes(self, changes: Dict[str, Any]) -> str:
        """Format preference changes for confirmation."""
        
        changes_text = "**Changes made:**\n"
        
        for pref, value in changes.items():
            pref_name = pref.replace("_", " ").title()
            status = "Enabled" if value else "Disabled"
            changes_text += f"• {pref_name}: {status}\n"
        
        return changes_text
    
    def _get_tool_by_name(self, tool_name: str):
        """Get a tool by name from loaded tools."""
        if not self._tools:
            return None
        
        for tool in self._tools:
            if hasattr(tool, 'name') and tool.name == tool_name:
                return tool
        return None
    
    def _initialize_profile_fields(self) -> Dict[str, Dict[str, Any]]:
        """Initialize profile field definitions."""
        return {
            "name": {"required": True, "type": "string", "min_length": 2},
            "email": {"required": True, "type": "email", "validation": "email_format"},
            "phone": {"required": False, "type": "phone", "validation": "phone_format"},
            "location": {"required": False, "type": "object", "fields": ["latitude", "longitude"]},
            "preferences": {"required": False, "type": "object", "nested": True}
        }
    
    def _initialize_privacy_settings(self) -> Dict[str, Dict[str, Any]]:
        """Initialize privacy settings definitions."""
        return {
            "data_sharing": {
                "location": {"default": True, "description": "Share location for better search results"},
                "analytics": {"default": True, "description": "Anonymous usage analytics"},
                "marketing": {"default": True, "description": "Receive marketing communications"}
            },
            "communication": {
                "email": {"default": True, "description": "Email notifications"},
                "whatsapp": {"default": True, "description": "WhatsApp messages"},
                "sms": {"default": False, "description": "SMS notifications"}
            },
            "security": {
                "two_factor": {"default": False, "description": "Two-factor authentication"},
                "login_notifications": {"default": True, "description": "Login attempt notifications"}
            }
        }
