"""Enhanced tool management with categorization and performance tracking."""

from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
import asyncio
import time

class ToolCategory(Enum):
    SEARCH = "search"
    BOOKING = "booking"
    USER_MANAGEMENT = "user_management"
    COMMUNICATION = "communication"
    CATEGORIES = "categories"

@dataclass
class ToolMetrics:
    """Track tool performance and usage."""
    call_count: int = 0
    total_execution_time: float = 0.0
    error_count: int = 0
    last_used: Optional[float] = None
    avg_execution_time: float = 0.0

class EnhancedToolManager:
    """Enhanced tool manager with categorization and metrics."""
    
    def __init__(self):
        self._tools_by_category: Dict[ToolCategory, List[Any]] = {}
        self._tool_metrics: Dict[str, ToolMetrics] = {}
        self._tools_cache: Optional[List[Any]] = None
        self._last_cache_time: float = 0
        self.cache_ttl: float = 300  # 5 minutes
    
    async def initialize(self):
        """Initialize and categorize tools from MCP server."""
        from shared.mcp_tools import get_catchup_tools
        
        all_tools = await get_catchup_tools()
        self._tools_cache = all_tools
        self._last_cache_time = time.time()
        
        # Categorize tools
        self._categorize_tools(all_tools)
        
        # Initialize metrics
        for tool in all_tools:
            if tool.name not in self._tool_metrics:
                self._tool_metrics[tool.name] = ToolMetrics()
    
    def _categorize_tools(self, tools: List[Any]):
        """Categorize tools based on their names and descriptions."""
        category_mapping = {
            ToolCategory.SEARCH: ['search_deals', 'search_businesses'],
            ToolCategory.BOOKING: ['create_booking', 'get_booking', 'update_booking'],
            ToolCategory.USER_MANAGEMENT: ['get_user', 'update_user'],
            ToolCategory.COMMUNICATION: ['send_email', 'send_whatsapp'],
            ToolCategory.CATEGORIES: ['get_categories']
        }
        
        # Initialize categories
        for category in ToolCategory:
            self._tools_by_category[category] = []
        
        # Categorize each tool
        for tool in tools:
            categorized = False
            for category, tool_names in category_mapping.items():
                if tool.name in tool_names:
                    self._tools_by_category[category].append(tool)
                    categorized = True
                    break
            
            # If not categorized, add to search as default
            if not categorized:
                self._tools_by_category[ToolCategory.SEARCH].append(tool)
    
    async def get_tools_for_categories(self, categories: Set[ToolCategory]) -> List[Any]:
        """Get tools for specific categories."""
        await self._ensure_tools_loaded()
        
        tools = []
        for category in categories:
            tools.extend(self._tools_by_category.get(category, []))
        
        return tools
    
    async def get_all_tools(self) -> List[Any]:
        """Get all tools with cache management."""
        await self._ensure_tools_loaded()
        return self._tools_cache or []
    
    async def _ensure_tools_loaded(self):
        """Ensure tools are loaded and cache is valid."""
        current_time = time.time()
        if (self._tools_cache is None or 
            current_time - self._last_cache_time > self.cache_ttl):
            await self.initialize()
    
    def record_tool_execution(self, tool_name: str, execution_time: float, success: bool):
        """Record tool execution metrics."""
        if tool_name not in self._tool_metrics:
            self._tool_metrics[tool_name] = ToolMetrics()
        
        metrics = self._tool_metrics[tool_name]
        metrics.call_count += 1
        metrics.total_execution_time += execution_time
        metrics.last_used = time.time()
        
        if not success:
            metrics.error_count += 1
        
        # Update average
        metrics.avg_execution_time = metrics.total_execution_time / metrics.call_count
    
    def get_tool_metrics(self, tool_name: str) -> Optional[ToolMetrics]:
        """Get metrics for a specific tool."""
        return self._tool_metrics.get(tool_name)
    
    def get_frequently_used_tools(self, limit: int = 5) -> List[str]:
        """Get most frequently used tools."""
        sorted_tools = sorted(
            self._tool_metrics.items(),
            key=lambda x: x[1].call_count,
            reverse=True
        )
        return [tool_name for tool_name, _ in sorted_tools[:limit]]