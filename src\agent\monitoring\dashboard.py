"""Monitoring dashboard and alerting system."""

import time
import asyncio
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from .metrics_collector import metrics_collector, MetricsCollector


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Alert:
    """Alert definition."""
    id: str
    name: str
    description: str
    severity: AlertSeverity
    metric_name: str
    condition: str  # e.g., "> 5.0", "< 0.8"
    threshold: float
    duration: float = 60.0  # seconds
    triggered_at: Optional[float] = None
    resolved_at: Optional[float] = None
    is_active: bool = False
    notification_sent: bool = False


class AlertManager:
    """Manages alerts and notifications."""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.alerts: Dict[str, Alert] = {}
        self.alert_handlers: List[Callable] = []
        self.check_interval = 30.0  # seconds
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        
        # Initialize default alerts
        self._initialize_default_alerts()
    
    def _initialize_default_alerts(self):
        """Initialize default system alerts."""
        
        # High response time alert
        self.add_alert(Alert(
            id="high_response_time",
            name="High Response Time",
            description="System response time is above acceptable threshold",
            severity=AlertSeverity.WARNING,
            metric_name="system_response_time_p95",
            condition="> 5.0",
            threshold=5.0,
            duration=120.0
        ))
        
        # Low agent health alert
        self.add_alert(Alert(
            id="low_agent_health",
            name="Low Agent Health",
            description="Less than 80% of agents are healthy",
            severity=AlertSeverity.ERROR,
            metric_name="system_healthy_agents_ratio",
            condition="< 0.8",
            threshold=0.8,
            duration=60.0
        ))
        
        # High error rate alert
        self.add_alert(Alert(
            id="high_error_rate",
            name="High Error Rate",
            description="Agent error rate is above threshold",
            severity=AlertSeverity.ERROR,
            metric_name="agent_errors_total",
            condition="> 50",
            threshold=50.0,
            duration=180.0
        ))
        
        # System overload alert
        self.add_alert(Alert(
            id="system_overload",
            name="System Overload",
            description="Too many active conversations",
            severity=AlertSeverity.WARNING,
            metric_name="system_active_conversations",
            condition="> 100",
            threshold=100.0,
            duration=300.0
        ))
    
    def add_alert(self, alert: Alert):
        """Add an alert to the system."""
        self.alerts[alert.id] = alert
    
    def remove_alert(self, alert_id: str):
        """Remove an alert from the system."""
        self.alerts.pop(alert_id, None)
    
    def add_alert_handler(self, handler: Callable):
        """Add an alert notification handler."""
        self.alert_handlers.append(handler)
    
    async def start_monitoring(self):
        """Start alert monitoring."""
        if not self._monitoring:
            self._monitoring = True
            self._monitor_task = asyncio.create_task(self._monitor_alerts_loop())
    
    async def stop_monitoring(self):
        """Stop alert monitoring."""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
    
    async def _monitor_alerts_loop(self):
        """Background loop for monitoring alerts."""
        while self._monitoring:
            try:
                await self._check_alerts()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                print(f"Error in alert monitoring: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _check_alerts(self):
        """Check all alerts against current metrics."""
        current_time = time.time()
        
        for alert in self.alerts.values():
            try:
                # Get current metric value
                metric_value = self._get_metric_value(alert.metric_name)
                if metric_value is None:
                    continue
                
                # Check condition
                condition_met = self._evaluate_condition(metric_value, alert.condition, alert.threshold)
                
                if condition_met and not alert.is_active:
                    # Potential trigger - check duration
                    if alert.triggered_at is None:
                        alert.triggered_at = current_time
                    elif current_time - alert.triggered_at >= alert.duration:
                        # Trigger alert
                        alert.is_active = True
                        alert.notification_sent = False
                        await self._trigger_alert(alert, metric_value)
                
                elif not condition_met and alert.is_active:
                    # Resolve alert
                    alert.is_active = False
                    alert.resolved_at = current_time
                    alert.triggered_at = None
                    await self._resolve_alert(alert, metric_value)
                
                elif not condition_met:
                    # Reset trigger time
                    alert.triggered_at = None
                
            except Exception as e:
                print(f"Error checking alert {alert.id}: {e}")
    
    def _get_metric_value(self, metric_name: str) -> Optional[float]:
        """Get the latest value for a metric."""
        
        # Handle special computed metrics
        if metric_name == "system_healthy_agents_ratio":
            healthy_metric = self.metrics_collector.metrics.get("system_healthy_agents")
            total_metric = self.metrics_collector.metrics.get("system_total_agents")
            
            if (healthy_metric and total_metric and 
                healthy_metric.data_points and total_metric.data_points):
                healthy = healthy_metric.data_points[-1].value
                total = total_metric.data_points[-1].value
                return healthy / total if total > 0 else 1.0
            return None
        
        # Regular metrics
        metric = self.metrics_collector.metrics.get(metric_name)
        if metric and metric.data_points:
            return metric.data_points[-1].value
        
        return None
    
    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """Evaluate if a condition is met."""
        condition = condition.strip()
        
        if condition.startswith(">"):
            return value > threshold
        elif condition.startswith("<"):
            return value < threshold
        elif condition.startswith(">="):
            return value >= threshold
        elif condition.startswith("<="):
            return value <= threshold
        elif condition.startswith("=="):
            return value == threshold
        elif condition.startswith("!="):
            return value != threshold
        
        return False
    
    async def _trigger_alert(self, alert: Alert, current_value: float):
        """Trigger an alert."""
        print(f"🚨 ALERT TRIGGERED: {alert.name}")
        print(f"   Description: {alert.description}")
        print(f"   Severity: {alert.severity.value}")
        print(f"   Current value: {current_value}")
        print(f"   Threshold: {alert.threshold}")
        
        # Notify handlers
        for handler in self.alert_handlers:
            try:
                await handler(alert, "triggered", current_value)
            except Exception as e:
                print(f"Error in alert handler: {e}")
    
    async def _resolve_alert(self, alert: Alert, current_value: float):
        """Resolve an alert."""
        print(f"✅ ALERT RESOLVED: {alert.name}")
        print(f"   Current value: {current_value}")
        
        # Notify handlers
        for handler in self.alert_handlers:
            try:
                await handler(alert, "resolved", current_value)
            except Exception as e:
                print(f"Error in alert handler: {e}")
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all currently active alerts."""
        return [alert for alert in self.alerts.values() if alert.is_active]
    
    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get alert history for the specified time period."""
        cutoff_time = time.time() - (hours * 3600)
        history = []
        
        for alert in self.alerts.values():
            if alert.triggered_at and alert.triggered_at >= cutoff_time:
                history.append({
                    "alert_id": alert.id,
                    "name": alert.name,
                    "severity": alert.severity.value,
                    "triggered_at": alert.triggered_at,
                    "resolved_at": alert.resolved_at,
                    "is_active": alert.is_active,
                    "duration": (alert.resolved_at or time.time()) - alert.triggered_at
                })
        
        return sorted(history, key=lambda x: x["triggered_at"], reverse=True)


class SystemDashboard:
    """System monitoring dashboard."""
    
    def __init__(self, metrics_collector: MetricsCollector, alert_manager: AlertManager):
        self.metrics_collector = metrics_collector
        self.alert_manager = alert_manager
    
    def get_system_overview(self) -> Dict[str, Any]:
        """Get system overview dashboard data."""
        
        # Get basic metrics
        metrics_summary = self.metrics_collector.get_all_metrics_summary()
        
        # Get agent status
        agent_status = self._get_agent_status_summary()
        
        # Get active alerts
        active_alerts = self.alert_manager.get_active_alerts()
        
        # Get bottlenecks
        bottlenecks = self.metrics_collector.identify_bottlenecks()
        
        # Calculate system health score
        health_score = self._calculate_system_health_score()
        
        return {
            "timestamp": time.time(),
            "system_health": {
                "score": health_score,
                "status": self._get_health_status(health_score),
                "active_alerts": len(active_alerts),
                "bottlenecks": len(bottlenecks)
            },
            "agents": agent_status,
            "metrics": {
                "total_metrics": len(metrics_summary["metrics"]),
                "collection_active": metrics_summary["collecting"],
                "collection_interval": metrics_summary["collection_interval"]
            },
            "alerts": {
                "active": len(active_alerts),
                "total_configured": len(self.alert_manager.alerts),
                "recent_history": len(self.alert_manager.get_alert_history(1))  # Last hour
            },
            "performance": {
                "bottlenecks": bottlenecks,
                "top_slow_operations": self._get_slow_operations()
            }
        }
    
    def get_agent_details(self, agent_type: str) -> Dict[str, Any]:
        """Get detailed information about a specific agent."""
        from agent.agents import agent_registry, AgentType
        
        try:
            agent_enum = AgentType(agent_type)
            agent = agent_registry.get_agent(agent_enum)
            
            if not agent:
                return {"error": f"Agent {agent_type} not found"}
            
            return {
                "agent_type": agent.agent_type.value,
                "name": agent.name,
                "status": agent.status.value,
                "metrics": {
                    "total_requests": agent.metrics.total_requests,
                    "successful_requests": agent.metrics.successful_requests,
                    "failed_requests": agent.metrics.failed_requests,
                    "avg_execution_time": agent.metrics.avg_execution_time,
                    "error_rate": agent.metrics.error_rate,
                    "last_execution": agent.metrics.last_execution
                },
                "health": {
                    "is_healthy": agent.status != agent.status.ERROR,
                    "error_rate_ok": agent.metrics.error_rate < 0.1,
                    "response_time_ok": agent.metrics.avg_execution_time < 5.0
                }
            }
            
        except ValueError:
            return {"error": f"Invalid agent type: {agent_type}"}
    
    def get_metrics_dashboard(self) -> Dict[str, Any]:
        """Get metrics dashboard data."""
        
        metrics_data = {}
        
        for name, metric in self.metrics_collector.metrics.items():
            if metric.data_points:
                # Get recent data points (last 100)
                recent_points = list(metric.data_points)[-100:]
                
                metrics_data[name] = {
                    "type": metric.metric_type.value,
                    "description": metric.description,
                    "unit": metric.unit,
                    "current_value": recent_points[-1].value,
                    "data_points": [
                        {"timestamp": p.timestamp, "value": p.value, "labels": p.labels}
                        for p in recent_points
                    ],
                    "summary": self.metrics_collector.get_metric_summary(name)
                }
        
        return {
            "timestamp": time.time(),
            "metrics": metrics_data,
            "collection_status": {
                "active": self.metrics_collector._collecting,
                "interval": self.metrics_collector.collection_interval
            }
        }
    
    def _get_agent_status_summary(self) -> Dict[str, Any]:
        """Get summary of agent statuses."""
        from agent.agents import agent_registry
        
        agents = agent_registry.get_all_agents()
        
        status_counts = {}
        total_requests = 0
        total_errors = 0
        
        for agent in agents:
            status = agent.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            total_requests += agent.metrics.total_requests
            total_errors += agent.metrics.failed_requests
        
        return {
            "total_agents": len(agents),
            "status_distribution": status_counts,
            "total_requests": total_requests,
            "total_errors": total_errors,
            "overall_error_rate": total_errors / max(total_requests, 1)
        }
    
    def _calculate_system_health_score(self) -> float:
        """Calculate overall system health score (0-100)."""
        score = 100.0
        
        # Deduct for active alerts
        active_alerts = self.alert_manager.get_active_alerts()
        for alert in active_alerts:
            if alert.severity == AlertSeverity.CRITICAL:
                score -= 30
            elif alert.severity == AlertSeverity.ERROR:
                score -= 20
            elif alert.severity == AlertSeverity.WARNING:
                score -= 10
            else:
                score -= 5
        
        # Deduct for bottlenecks
        bottlenecks = self.metrics_collector.identify_bottlenecks()
        for bottleneck in bottlenecks:
            if bottleneck.get("severity") == "high":
                score -= 15
            else:
                score -= 10
        
        # Deduct for unhealthy agents
        from agent.agents import agent_registry
        agents = agent_registry.get_all_agents()
        if agents:
            unhealthy_ratio = sum(1 for a in agents if a.status.value == "error") / len(agents)
            score -= unhealthy_ratio * 25
        
        return max(0.0, min(100.0, score))
    
    def _get_health_status(self, score: float) -> str:
        """Get health status based on score."""
        if score >= 90:
            return "excellent"
        elif score >= 75:
            return "good"
        elif score >= 50:
            return "fair"
        elif score >= 25:
            return "poor"
        else:
            return "critical"
    
    def _get_slow_operations(self) -> List[Dict[str, Any]]:
        """Get slowest operations."""
        slow_ops = []
        
        duration_metric = self.metrics_collector.metrics.get("agent_request_duration_seconds")
        if duration_metric and duration_metric.data_points:
            # Group by agent and find slowest
            agent_times = {}
            for point in duration_metric.data_points:
                agent = point.labels.get("agent_type", "unknown")
                if agent not in agent_times:
                    agent_times[agent] = []
                agent_times[agent].append(point.value)
            
            for agent, times in agent_times.items():
                avg_time = sum(times) / len(times)
                max_time = max(times)
                
                if avg_time > 1.0:  # Slower than 1 second
                    slow_ops.append({
                        "agent": agent,
                        "avg_time": avg_time,
                        "max_time": max_time,
                        "sample_count": len(times)
                    })
        
        return sorted(slow_ops, key=lambda x: x["avg_time"], reverse=True)[:5]


# Global instances
alert_manager = AlertManager(metrics_collector)
dashboard = SystemDashboard(metrics_collector, alert_manager)
