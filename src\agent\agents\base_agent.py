"""Base agent class for the CatchUp agentic architecture."""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
from enum import Enum
import time
import asyncio
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig

from agent.state import CatchUpState
from agent.tools.tool_manager import ToolCategory


class AgentType(Enum):
    """Types of agents in the system."""
    ORCHESTRATOR = "orchestrator"
    INTENT_ANALYSIS = "intent_analysis"
    SEARCH = "search"
    BOOKING = "booking"
    COMMUNICATION = "communication"
    USER_MANAGEMENT = "user_management"
    ERROR_HANDLING = "error_handling"
    QUALITY_ASSURANCE = "quality_assurance"


class AgentStatus(Enum):
    """Agent execution status."""
    IDLE = "idle"
    PROCESSING = "processing"
    ERROR = "error"
    COMPLETED = "completed"


@dataclass
class AgentMetrics:
    """Track agent performance metrics."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_execution_time: float = 0.0
    avg_execution_time: float = 0.0
    last_execution: Optional[float] = None
    error_rate: float = 0.0


@dataclass
class AgentContext:
    """Context passed between agents."""
    agent_type: AgentType
    request_id: str
    user_intent: Optional[str] = None
    confidence: Optional[float] = None
    required_tools: Optional[List[str]] = None
    error_info: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class BaseAgent(ABC):
    """Base class for all agents in the CatchUp system."""
    
    def __init__(self, agent_type: AgentType, name: str):
        """Initialize the base agent.
        
        Args:
            agent_type: The type of agent
            name: Human-readable name for the agent
        """
        self.agent_type = agent_type
        self.name = name
        self.status = AgentStatus.IDLE
        self.metrics = AgentMetrics()
        self.required_tool_categories: Set[ToolCategory] = set()
        self._tools: Optional[List[Any]] = None
        
    @abstractmethod
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Process a request and return the result.
        
        Args:
            state: Current conversation state
            config: Runtime configuration
            context: Agent context for inter-agent communication
            
        Returns:
            Dictionary containing the agent's response
        """
        pass
    
    @abstractmethod
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Check if this agent can handle the given intent.
        
        Args:
            intent: The user intent to check
            context: Additional context for decision making
            
        Returns:
            True if the agent can handle this intent
        """
        pass
    
    async def execute_with_metrics(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Execute the agent with performance tracking.
        
        Args:
            state: Current conversation state
            config: Runtime configuration
            context: Agent context
            
        Returns:
            Agent response with metrics
        """
        start_time = time.time()
        self.status = AgentStatus.PROCESSING
        self.metrics.total_requests += 1
        
        try:
            result = await self.process(state, config, context)
            
            # Update success metrics
            execution_time = time.time() - start_time
            self.metrics.successful_requests += 1
            self.metrics.total_execution_time += execution_time
            self.metrics.avg_execution_time = (
                self.metrics.total_execution_time / self.metrics.total_requests
            )
            self.metrics.last_execution = time.time()
            self.status = AgentStatus.COMPLETED
            
            return result
            
        except Exception as e:
            # Update error metrics
            self.metrics.failed_requests += 1
            self.metrics.error_rate = (
                self.metrics.failed_requests / self.metrics.total_requests
            )
            self.status = AgentStatus.ERROR
            
            # Return error response
            return {
                "messages": [AIMessage(
                    content=f"Agent {self.name} encountered an error: {str(e)}"
                )],
                "agent_error": {
                    "agent_type": self.agent_type.value,
                    "error": str(e),
                    "timestamp": time.time()
                }
            }
    
    async def load_tools(self):
        """Load tools required by this agent."""
        if self.required_tool_categories:
            from agent.tools.tool_manager import EnhancedToolManager
            tool_manager = EnhancedToolManager()
            await tool_manager.initialize()
            self._tools = await tool_manager.get_tools_for_categories(
                self.required_tool_categories
            )
    
    def get_metrics(self) -> AgentMetrics:
        """Get current agent metrics."""
        return self.metrics
    
    def reset_metrics(self):
        """Reset agent metrics."""
        self.metrics = AgentMetrics()
    
    def get_status(self) -> AgentStatus:
        """Get current agent status."""
        return self.status


class AgentRegistry:
    """Registry for managing all agents in the system."""
    
    def __init__(self):
        self._agents: Dict[AgentType, BaseAgent] = {}
        self._agent_instances: List[BaseAgent] = []
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent in the system.
        
        Args:
            agent: The agent to register
        """
        self._agents[agent.agent_type] = agent
        self._agent_instances.append(agent)
    
    def get_agent(self, agent_type: AgentType) -> Optional[BaseAgent]:
        """Get an agent by type.
        
        Args:
            agent_type: The type of agent to retrieve
            
        Returns:
            The agent instance or None if not found
        """
        return self._agents.get(agent_type)
    
    def get_all_agents(self) -> List[BaseAgent]:
        """Get all registered agents."""
        return self._agent_instances.copy()
    
    def get_agents_for_intent(self, intent: str) -> List[BaseAgent]:
        """Get all agents that can handle a specific intent.
        
        Args:
            intent: The user intent
            
        Returns:
            List of agents that can handle the intent
        """
        return [
            agent for agent in self._agent_instances 
            if agent.can_handle(intent)
        ]
    
    async def initialize_all_agents(self):
        """Initialize all registered agents."""
        for agent in self._agent_instances:
            await agent.load_tools()


# Global agent registry
agent_registry = AgentRegistry()
