#!/usr/bin/env python3
"""Test script to verify the graph implementation with mcp_tools and conditional_edge_1."""

import asyncio
import sys
import os

# Add src to path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from langchain_core.messages import HumanMessage, AIMessage
from agent.state import CatchUpState
from agent.graph import mcp_tools, conditional_edge_1


async def test_conditional_edge():
    """Test the conditional edge function."""
    print("Testing conditional_edge_1...")
    
    # Test case 1: No messages
    state_empty = CatchUpState(messages=[])
    result = conditional_edge_1(state_empty)
    print(f"Empty state: {result}")
    assert result == "__end__"
    
    # Test case 2: Human message (no tool calls)
    state_human = CatchUpState(messages=[HumanMessage(content="Hello")])
    result = conditional_edge_1(state_human)
    print(f"Human message: {result}")
    assert result == "__end__"
    
    # Test case 3: AI message without tool calls
    ai_msg_no_tools = AIMessage(content="Hello, how can I help you?")
    state_ai_no_tools = CatchUpState(messages=[ai_msg_no_tools])
    result = conditional_edge_1(state_ai_no_tools)
    print(f"AI message without tools: {result}")
    assert result == "__end__"
    
    # Test case 4: AI message with tool calls
    ai_msg_with_tools = AIMessage(
        content="I'll help you find categories.",
        tool_calls=[
            {
                "name": "get_categories",
                "args": {},
                "id": "call_123"
            }
        ]
    )
    state_ai_with_tools = CatchUpState(messages=[ai_msg_with_tools])
    result = conditional_edge_1(state_ai_with_tools)
    print(f"AI message with tools: {result}")
    assert result == "mcp_tools"
    
    print("✓ All conditional_edge_1 tests passed!")


async def test_mcp_tools_function():
    """Test that the mcp_tools function is properly defined."""
    print("\nTesting mcp_tools function...")
    
    # Check if function exists and is callable
    assert callable(mcp_tools)
    print("✓ mcp_tools function is callable")
    
    # Note: We can't easily test the actual execution without a full setup
    # but we can verify the function signature
    import inspect
    sig = inspect.signature(mcp_tools)
    params = list(sig.parameters.keys())
    print(f"Function parameters: {params}")
    assert "state" in params
    assert "config" in params
    print("✓ mcp_tools function has correct signature")


async def test_graph_compilation():
    """Test that the graph compiles without errors."""
    print("\nTesting graph compilation...")
    
    try:
        from agent.graph import graph
        print("✓ Graph imported successfully")
        
        # Check that graph has the expected structure
        assert graph is not None
        print("✓ Graph is not None")
        
        print("✓ Graph compilation test passed!")
        
    except Exception as e:
        print(f"✗ Graph compilation failed: {e}")
        raise


async def main():
    """Run all tests."""
    print("Running graph implementation tests...\n")
    
    try:
        await test_conditional_edge()
        await test_mcp_tools_function()
        await test_graph_compilation()
        
        print("\n" + "="*50)
        print("✓ All tests passed! Implementation looks good.")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    asyncio.run(main())
