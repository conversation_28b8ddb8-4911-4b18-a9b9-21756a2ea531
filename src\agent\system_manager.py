"""System manager for the agentic architecture."""

import asyncio
import time
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from agent.agents import agent_registry, AgentType
from agent.communication import communication_bus
from agent.monitoring import metrics_collector, alert_manager, dashboard


class SystemManager:
    """Central system manager for the agentic architecture."""
    
    def __init__(self):
        self.is_initialized = False
        self.is_running = False
        self.startup_time: Optional[float] = None
        self.shutdown_time: Optional[float] = None
        
    async def initialize(self):
        """Initialize the entire agentic system."""
        if self.is_initialized:
            return
        
        print("🚀 Initializing CatchUp Agentic System...")
        
        try:
            # Initialize agents
            print("   📋 Initializing agents...")
            await agent_registry.initialize_all_agents()
            print(f"   ✅ Initialized {len(agent_registry.get_all_agents())} agents")
            
            # Start communication bus
            print("   📡 Starting communication bus...")
            await communication_bus.start()
            print("   ✅ Communication bus started")
            
            # Start metrics collection
            print("   📊 Starting metrics collection...")
            await metrics_collector.start_collection()
            print("   ✅ Metrics collection started")
            
            # Start alert monitoring
            print("   🚨 Starting alert monitoring...")
            await alert_manager.start_monitoring()
            print("   ✅ Alert monitoring started")
            
            # Register system metrics
            self._register_system_metrics()
            
            # Set up alert handlers
            self._setup_alert_handlers()
            
            self.is_initialized = True
            self.startup_time = time.time()
            
            print("✅ CatchUp Agentic System initialized successfully!")
            print(f"   🎯 Agents: {len(agent_registry.get_all_agents())}")
            print(f"   📊 Metrics: {len(metrics_collector.metrics)}")
            print(f"   🚨 Alerts: {len(alert_manager.alerts)}")
            
        except Exception as e:
            print(f"❌ Failed to initialize system: {e}")
            raise
    
    async def start(self):
        """Start the system services."""
        if not self.is_initialized:
            await self.initialize()
        
        if self.is_running:
            return
        
        print("🔄 Starting system services...")
        
        # All services are already started in initialize()
        # This method is for future extensibility
        
        self.is_running = True
        print("✅ System services started")
    
    async def stop(self):
        """Stop the system services."""
        if not self.is_running:
            return
        
        print("🛑 Stopping system services...")
        
        try:
            # Stop alert monitoring
            print("   🚨 Stopping alert monitoring...")
            await alert_manager.stop_monitoring()
            
            # Stop metrics collection
            print("   📊 Stopping metrics collection...")
            await metrics_collector.stop_collection()
            
            # Stop communication bus
            print("   📡 Stopping communication bus...")
            await communication_bus.stop()
            
            self.is_running = False
            self.shutdown_time = time.time()
            
            print("✅ System services stopped")
            
        except Exception as e:
            print(f"❌ Error stopping system: {e}")
            raise
    
    async def restart(self):
        """Restart the system."""
        print("🔄 Restarting system...")
        await self.stop()
        await asyncio.sleep(1)  # Brief pause
        await self.start()
        print("✅ System restarted")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        
        # Get agent status
        agents = agent_registry.get_all_agents()
        agent_status = {
            "total": len(agents),
            "by_type": {},
            "by_status": {},
            "healthy": 0
        }
        
        for agent in agents:
            # Count by type
            agent_type = agent.agent_type.value
            agent_status["by_type"][agent_type] = agent_status["by_type"].get(agent_type, 0) + 1
            
            # Count by status
            status = agent.status.value
            agent_status["by_status"][status] = agent_status["by_status"].get(status, 0) + 1
            
            # Count healthy agents
            if agent.status.value != "error":
                agent_status["healthy"] += 1
        
        # Get communication status
        comm_stats = communication_bus.get_stats()
        
        # Get monitoring status
        active_alerts = alert_manager.get_active_alerts()
        bottlenecks = metrics_collector.identify_bottlenecks()
        
        # Calculate uptime
        uptime = None
        if self.startup_time:
            uptime = time.time() - self.startup_time
        
        return {
            "system": {
                "initialized": self.is_initialized,
                "running": self.is_running,
                "uptime_seconds": uptime,
                "startup_time": self.startup_time,
                "shutdown_time": self.shutdown_time
            },
            "agents": agent_status,
            "communication": {
                "bus_running": communication_bus._running,
                "stats": comm_stats
            },
            "monitoring": {
                "metrics_collecting": metrics_collector._collecting,
                "alert_monitoring": alert_manager._monitoring,
                "active_alerts": len(active_alerts),
                "bottlenecks": len(bottlenecks),
                "total_metrics": len(metrics_collector.metrics)
            },
            "health": {
                "score": dashboard._calculate_system_health_score(),
                "status": dashboard._get_health_status(dashboard._calculate_system_health_score()),
                "issues": len(active_alerts) + len(bottlenecks)
            }
        }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get system performance report."""
        
        # Get metrics summary
        metrics_summary = metrics_collector.get_all_metrics_summary()
        
        # Get agent performance
        agents = agent_registry.get_all_agents()
        agent_performance = []
        
        for agent in agents:
            agent_performance.append({
                "name": agent.name,
                "type": agent.agent_type.value,
                "total_requests": agent.metrics.total_requests,
                "success_rate": (
                    agent.metrics.successful_requests / max(agent.metrics.total_requests, 1)
                ),
                "avg_response_time": agent.metrics.avg_execution_time,
                "error_rate": agent.metrics.error_rate
            })
        
        # Sort by total requests (most active first)
        agent_performance.sort(key=lambda x: x["total_requests"], reverse=True)
        
        # Get bottlenecks
        bottlenecks = metrics_collector.identify_bottlenecks()
        
        # Get recent alerts
        recent_alerts = alert_manager.get_alert_history(24)  # Last 24 hours
        
        return {
            "timestamp": time.time(),
            "summary": {
                "total_agents": len(agents),
                "total_requests": sum(a.metrics.total_requests for a in agents),
                "total_errors": sum(a.metrics.failed_requests for a in agents),
                "avg_response_time": sum(a.metrics.avg_execution_time for a in agents) / max(len(agents), 1),
                "system_health_score": dashboard._calculate_system_health_score()
            },
            "agent_performance": agent_performance,
            "bottlenecks": bottlenecks,
            "recent_alerts": recent_alerts,
            "metrics_summary": metrics_summary
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        
        health_status = {
            "overall_status": "healthy",
            "timestamp": time.time(),
            "checks": {},
            "issues": []
        }
        
        # Check system initialization
        health_status["checks"]["system_initialized"] = {
            "status": "pass" if self.is_initialized else "fail",
            "message": "System is initialized" if self.is_initialized else "System not initialized"
        }
        
        # Check system running
        health_status["checks"]["system_running"] = {
            "status": "pass" if self.is_running else "fail", 
            "message": "System is running" if self.is_running else "System not running"
        }
        
        # Check agents
        agents = agent_registry.get_all_agents()
        healthy_agents = sum(1 for a in agents if a.status.value != "error")
        agent_health_ratio = healthy_agents / max(len(agents), 1)
        
        health_status["checks"]["agents_healthy"] = {
            "status": "pass" if agent_health_ratio >= 0.8 else "fail",
            "message": f"{healthy_agents}/{len(agents)} agents healthy",
            "ratio": agent_health_ratio
        }
        
        # Check communication bus
        health_status["checks"]["communication_bus"] = {
            "status": "pass" if communication_bus._running else "fail",
            "message": "Communication bus is running" if communication_bus._running else "Communication bus not running"
        }
        
        # Check metrics collection
        health_status["checks"]["metrics_collection"] = {
            "status": "pass" if metrics_collector._collecting else "fail",
            "message": "Metrics collection active" if metrics_collector._collecting else "Metrics collection inactive"
        }
        
        # Check for active alerts
        active_alerts = alert_manager.get_active_alerts()
        critical_alerts = [a for a in active_alerts if a.severity.value == "critical"]
        
        health_status["checks"]["no_critical_alerts"] = {
            "status": "pass" if not critical_alerts else "fail",
            "message": f"{len(critical_alerts)} critical alerts active" if critical_alerts else "No critical alerts",
            "active_alerts": len(active_alerts)
        }
        
        # Determine overall status
        failed_checks = [name for name, check in health_status["checks"].items() if check["status"] == "fail"]
        
        if failed_checks:
            health_status["overall_status"] = "unhealthy"
            health_status["issues"] = failed_checks
        
        return health_status
    
    def _register_system_metrics(self):
        """Register additional system-specific metrics."""
        
        # System uptime metric
        metrics_collector.register_metric(
            "system_uptime_seconds",
            metrics_collector.MetricType.GAUGE,
            "System uptime in seconds",
            "seconds"
        )
        
        # Agent registry metrics
        metrics_collector.register_metric(
            "agent_registry_size",
            metrics_collector.MetricType.GAUGE,
            "Number of registered agents"
        )
    
    def _setup_alert_handlers(self):
        """Set up alert notification handlers."""
        
        async def console_alert_handler(alert, action, value):
            """Simple console alert handler."""
            if action == "triggered":
                print(f"🚨 ALERT: {alert.name} - {alert.description} (Value: {value})")
            elif action == "resolved":
                print(f"✅ RESOLVED: {alert.name} (Value: {value})")
        
        alert_manager.add_alert_handler(console_alert_handler)


# Global system manager instance
system_manager = SystemManager()


# Context manager for system lifecycle
@asynccontextmanager
async def agentic_system():
    """Context manager for the agentic system lifecycle."""
    try:
        await system_manager.start()
        yield system_manager
    finally:
        await system_manager.stop()


# Utility functions
async def quick_start():
    """Quick start the system with default configuration."""
    await system_manager.start()
    return system_manager


async def quick_stop():
    """Quick stop the system."""
    await system_manager.stop()


def get_system_info():
    """Get basic system information."""
    return {
        "version": "1.0.0",
        "architecture": "agentic",
        "agents": [agent.agent_type.value for agent in agent_registry.get_all_agents()],
        "status": system_manager.get_system_status(),
        "initialized": system_manager.is_initialized,
        "running": system_manager.is_running
    }
