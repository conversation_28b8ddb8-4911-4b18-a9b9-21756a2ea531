"""Error Handling Agent for centralized error management and recovery."""

import asyncio
import time
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext
from agent.state import CatchUpState


class ErrorType(Enum):
    """Classification of error types."""
    TRANSIENT = "transient"  # Network, API timeouts
    VALIDATION = "validation"  # Invalid input, missing data
    BUSINESS_LOGIC = "business_logic"  # No results, unavailable service
    SYSTEM = "system"  # Database down, service unavailable
    CRITICAL = "critical"  # Security, data corruption


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    REQUEST_CLARIFICATION = "request_clarification"
    SUGGEST_ALTERNATIVES = "suggest_alternatives"
    GRACEFUL_FALLBACK = "graceful_fallback"
    ESCALATE = "escalate"


@dataclass
class ErrorClassification:
    """Error classification result."""
    error_type: ErrorType
    recovery_strategy: RecoveryStrategy
    retry_count: int = 0
    max_retries: int = 3
    user_message: str = ""
    escalation_required: bool = False


class ErrorHandlingAgent(BaseAgent):
    """Centralized error management and recovery agent."""
    
    def __init__(self):
        super().__init__(AgentType.ERROR_HANDLING, "Error Handling Agent")
        self.error_patterns = self._initialize_error_patterns()
        self.retry_attempts: Dict[str, int] = {}
        self.escalated_errors: List[Dict[str, Any]] = []
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Process and recover from errors."""
        
        if not context or not context.error_info:
            return {"messages": [AIMessage(content="No error information provided.")]}
        
        error_info = context.error_info
        error_message = error_info.get("error", "Unknown error")
        stage = error_info.get("stage", "unknown")
        original_intent = error_info.get("original_intent")
        
        # Classify the error
        classification = self._classify_error(error_message, error_info)
        
        # Track retry attempts
        request_id = context.request_id
        if request_id not in self.retry_attempts:
            self.retry_attempts[request_id] = 0
        
        # Execute recovery strategy
        recovery_result = await self._execute_recovery_strategy(
            classification, state, config, context
        )
        
        # Log error for monitoring
        self._log_error(error_info, classification, context)
        
        return recovery_result
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Error handling agent handles all error-related intents."""
        return intent in ["error_recovery", "system_error", "tool_error"]
    
    def _classify_error(self, error_message: str, error_info: Dict[str, Any]) -> ErrorClassification:
        """Classify error and determine recovery strategy."""
        
        error_lower = error_message.lower()
        
        # Check for transient errors
        if any(pattern in error_lower for pattern in self.error_patterns[ErrorType.TRANSIENT]):
            return ErrorClassification(
                error_type=ErrorType.TRANSIENT,
                recovery_strategy=RecoveryStrategy.RETRY,
                max_retries=3,
                user_message="I'm processing your request, please wait a moment..."
            )
        
        # Check for validation errors
        if any(pattern in error_lower for pattern in self.error_patterns[ErrorType.VALIDATION]):
            return ErrorClassification(
                error_type=ErrorType.VALIDATION,
                recovery_strategy=RecoveryStrategy.REQUEST_CLARIFICATION,
                user_message="I need some additional information to help you better."
            )
        
        # Check for business logic errors
        if any(pattern in error_lower for pattern in self.error_patterns[ErrorType.BUSINESS_LOGIC]):
            return ErrorClassification(
                error_type=ErrorType.BUSINESS_LOGIC,
                recovery_strategy=RecoveryStrategy.SUGGEST_ALTERNATIVES,
                user_message="I couldn't find exactly what you're looking for, but I can suggest some alternatives."
            )
        
        # Check for critical errors
        if any(pattern in error_lower for pattern in self.error_patterns[ErrorType.CRITICAL]):
            return ErrorClassification(
                error_type=ErrorType.CRITICAL,
                recovery_strategy=RecoveryStrategy.ESCALATE,
                escalation_required=True,
                user_message="I'm experiencing a technical issue. Let me connect you with a support agent."
            )
        
        # Default to system error
        return ErrorClassification(
            error_type=ErrorType.SYSTEM,
            recovery_strategy=RecoveryStrategy.GRACEFUL_FALLBACK,
            user_message="I'm experiencing some technical difficulties. Please try again in a moment."
        )
    
    async def _execute_recovery_strategy(
        self, 
        classification: ErrorClassification,
        state: CatchUpState,
        config: RunnableConfig,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Execute the appropriate recovery strategy."""
        
        strategy = classification.recovery_strategy
        request_id = context.request_id
        
        if strategy == RecoveryStrategy.RETRY:
            return await self._handle_retry(classification, state, config, context)
        
        elif strategy == RecoveryStrategy.REQUEST_CLARIFICATION:
            return await self._request_clarification(classification, state, context)
        
        elif strategy == RecoveryStrategy.SUGGEST_ALTERNATIVES:
            return await self._suggest_alternatives(classification, state, context)
        
        elif strategy == RecoveryStrategy.GRACEFUL_FALLBACK:
            return await self._graceful_fallback(classification, state, context)
        
        elif strategy == RecoveryStrategy.ESCALATE:
            return await self._escalate_to_human(classification, state, context)
        
        else:
            return {"messages": [AIMessage(content=classification.user_message)]}
    
    async def _handle_retry(
        self, 
        classification: ErrorClassification,
        state: CatchUpState,
        config: RunnableConfig,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Handle retry strategy with exponential backoff."""
        
        request_id = context.request_id
        retry_count = self.retry_attempts.get(request_id, 0)
        
        if retry_count >= classification.max_retries:
            # Max retries reached, fallback to graceful degradation
            return await self._graceful_fallback(classification, state, context)
        
        # Increment retry count
        self.retry_attempts[request_id] = retry_count + 1
        
        # Exponential backoff
        wait_time = min(2 ** retry_count, 10)  # Max 10 seconds
        await asyncio.sleep(wait_time)
        
        # Return retry message
        return {
            "messages": [AIMessage(content=classification.user_message)],
            "retry_info": {
                "attempt": retry_count + 1,
                "max_retries": classification.max_retries,
                "wait_time": wait_time
            }
        }
    
    async def _request_clarification(
        self, 
        classification: ErrorClassification,
        state: CatchUpState,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Request clarification from the user."""
        
        clarification_message = (
            "I need some additional information to help you better. "
            "Could you please provide more details about what you're looking for?"
        )
        
        return {
            "messages": [AIMessage(content=clarification_message)],
            "requires_clarification": True
        }
    
    async def _suggest_alternatives(
        self, 
        classification: ErrorClassification,
        state: CatchUpState,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Suggest alternatives when the original request can't be fulfilled."""
        
        alternatives_message = (
            "I couldn't find exactly what you're looking for, but here are some alternatives:\n"
            "• Try searching with different keywords\n"
            "• Check for similar services in nearby areas\n"
            "• Browse our popular categories for inspiration\n"
            "• Contact our support team for personalized assistance"
        )
        
        return {
            "messages": [AIMessage(content=alternatives_message)],
            "suggested_alternatives": True
        }
    
    async def _graceful_fallback(
        self, 
        classification: ErrorClassification,
        state: CatchUpState,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Provide graceful fallback response."""
        
        fallback_message = (
            "I'm experiencing some technical difficulties at the moment. "
            "Please try again in a few minutes. If the problem persists, "
            "our support team is available to assist you."
        )
        
        return {
            "messages": [AIMessage(content=fallback_message)],
            "fallback_response": True,
            "suggested_actions": [
                "Try again in a few minutes",
                "Contact support if issue persists",
                "Check our help center for common solutions"
            ]
        }
    
    async def _escalate_to_human(
        self, 
        classification: ErrorClassification,
        state: CatchUpState,
        context: AgentContext
    ) -> Dict[str, Any]:
        """Escalate critical issues to human support."""
        
        # Log escalation
        escalation_info = {
            "timestamp": time.time(),
            "request_id": context.request_id,
            "error_type": classification.error_type.value,
            "user_id": state.get("user_context", {}).get("user_id"),
            "session_id": state.get("session_config", {}).get("session_id")
        }
        self.escalated_errors.append(escalation_info)
        
        escalation_message = (
            "I'm experiencing a technical issue that requires immediate attention. "
            "I'm connecting you with a support agent who will assist you shortly. "
            "Your request ID is: " + context.request_id[:8]
        )
        
        return {
            "messages": [AIMessage(content=escalation_message)],
            "escalation_required": True,
            "escalation_info": escalation_info
        }
    
    def _initialize_error_patterns(self) -> Dict[ErrorType, List[str]]:
        """Initialize error pattern matching."""
        return {
            ErrorType.TRANSIENT: [
                "timeout", "connection", "network", "temporary", "retry",
                "service unavailable", "502", "503", "504"
            ],
            ErrorType.VALIDATION: [
                "invalid", "missing", "required", "format", "validation",
                "parameter", "argument", "input"
            ],
            ErrorType.BUSINESS_LOGIC: [
                "not found", "no results", "unavailable", "out of stock",
                "fully booked", "no availability", "closed"
            ],
            ErrorType.SYSTEM: [
                "database", "internal server", "500", "system error",
                "maintenance", "down", "offline"
            ],
            ErrorType.CRITICAL: [
                "security", "unauthorized", "forbidden", "corruption",
                "data loss", "breach", "attack"
            ]
        }
    
    def _log_error(
        self, 
        error_info: Dict[str, Any], 
        classification: ErrorClassification,
        context: AgentContext
    ):
        """Log error for monitoring and analysis."""
        print(f"ERROR LOGGED: {classification.error_type.value} - {error_info.get('error', 'Unknown')}")
        # In production, this would integrate with logging/monitoring systems
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error handling statistics."""
        return {
            "total_errors_handled": self.metrics.total_requests,
            "escalated_errors": len(self.escalated_errors),
            "retry_attempts": len(self.retry_attempts),
            "error_rate": self.metrics.error_rate
        }
