"""Intent node for analyzing search queries in the CatchUp customer service system."""
from agent.prompts import INTENT_SYSTEM_PROMPT
from langchain_core.messages import SystemMessage,HumanMessage
from langchain_core.runnables import RunnableConfig
from agent.state import CatchUpState
from shared import create_llm
from agent.state import <PERSON>U<PERSON><PERSON>tate
from shared.message_utils import limit_message_history

def chatbot(state: CatchUpState):
    return {"messages": [HumanMessage(content="Ciao")]}

async def intent_node(state: CatchUpState, config: RunnableConfig) -> str:
    """Analyze a search query and explain what the user is looking for.
    
    Args:
        query: The search query to analyze
        context: Previous conversation context (query/response pairs)
        llm: The language model to use for analysis
        
    Returns:
        Analysis of the query explaining what the user wants to know
    """
    configuration = config.get("configurable", {})

    # Get model configuration
    model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")

    # Create LLM instance
    llm = create_llm(model_name)

    

    # Create system prompt
    system_prompt =INTENT_SYSTEM_PROMPT

    # Apply memory length limiting (overrides reducer default if more restrictive)
    conversation_messages = state["messages"]  # Already limited by reducer to 15
    memory_length_str = state.get("memory_lenght", "15")

    try:
        memory_length = int(memory_length_str)
    except (ValueError, TypeError):
        memory_length = 15

    # Apply additional limiting if user wants fewer messages
    limited_messages = limit_message_history(conversation_messages, memory_length)

    # Prepare messages with system prompt and limited conversation history
    messages = [SystemMessage(content=system_prompt)] + limited_messages

    # Generate response using LLM with bound tools
    response = await llm.ainvoke(messages)

    return {"messages": [response]}