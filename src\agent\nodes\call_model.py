


from agent.prompts import create_system_prompt
from langchain_core.messages import SystemMessage
from langchain_core.runnables import RunnableConfig
from typing import Any, Dict
from datetime import datetime

from agent.state import CatchUpState, ConversationPhase, update_conversation_metrics
from shared import create_llm
from shared.mcp_tools import get_catchup_tools


async def call_model(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Enhanced call_model with better state management."""
    
    configuration = config.get("configurable", {})
    model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")
    
    # Create LLM instance
    llm = create_llm(model_name)
    
    # Get tools
    tools = await get_catchup_tools()
    llm_with_tools = llm.bind_tools(tools)
    
    # Use existing system prompt function
    system_prompt = create_system_prompt(state)
    
    # Prepare messages with system prompt
    conversation_messages = state["messages"]
    
    # Apply memory budget from session config
    memory_budget = state['session_config'].get('memory_budget', 15)
    if len(conversation_messages) > memory_budget:
        conversation_messages = conversation_messages[-memory_budget:]
    
    # Add system message
    messages_with_system = [SystemMessage(content=system_prompt)] + conversation_messages
    
    # Generate response
    try:
        start_time = datetime.now()
        response = await llm_with_tools.ainvoke(messages_with_system)
        end_time = datetime.now()
        
        # Estimate tokens (rough calculation)
        estimated_tokens = len(str(messages_with_system)) // 4 + len(str(response)) // 4
        
        # Update conversation metrics
        tool_calls = len(response.tool_calls) if hasattr(response, 'tool_calls') and response.tool_calls else 0
        state = update_conversation_metrics(
            state,
            tool_calls=tool_calls,
            estimated_tokens=estimated_tokens
        )
        
        # Update conversation phase based on response
        state = update_conversation_phase(state, response)
        
        # Update response context
        state['response_context'] = {
            'response_time_ms': (end_time - start_time).total_seconds() * 1000,
            'has_tool_calls': tool_calls > 0,
            'response_length': len(response.content) if response.content else 0
        }
        
        return {"messages": [response]}
        
    except Exception as e:
        print(f"Error in call_model: {e}")
        # Update metrics for error
        state = update_conversation_metrics(state, failed_tools=1)
        
        # Return error response
        from langchain_core.messages import AIMessage
        error_response = AIMessage(
            content="I apologize, but I encountered an error while processing your request. Please try again."
        )
        return {"messages": [error_response]}

def update_conversation_phase(state: CatchUpState, response) -> CatchUpState:
    """Update conversation phase based on the response."""
    
    current_phase = state['current_phase']
    
    # Simple phase progression logic
    if hasattr(response, 'tool_calls') and response.tool_calls:
        state['current_phase'] = ConversationPhase.TOOL_EXECUTION
    elif current_phase == ConversationPhase.TOOL_EXECUTION:
        state['current_phase'] = ConversationPhase.RESULT_PRESENTATION
    elif current_phase == ConversationPhase.GREETING and len(state['messages']) > 1:
        state['current_phase'] = ConversationPhase.INTENT_DISCOVERY
    elif current_phase == ConversationPhase.INTENT_DISCOVERY:
        state['current_phase'] = ConversationPhase.INFORMATION_GATHERING
    
    return state
