"""Agentic LangGraph implementation with orchestrator and specialized agents."""

from __future__ import annotations

import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from typing import TypedDict, Any, Dict
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, START, END

from agent.state import CatchUpState
from agent.agents import (
    orchestrator_agent, 
    agent_registry,
    AgentType
)


class Configuration(TypedDict):
    """Configurable parameters for the agentic chatbot."""
    model_name: str
    system_prompt: str


async def orchestrator_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Main orchestrator node that coordinates all agent interactions."""
    
    # Initialize all agents if not already done
    await agent_registry.initialize_all_agents()
    
    # Process through orchestrator
    result = await orchestrator_agent.execute_with_metrics(state, config)
    
    return result


async def intent_analysis_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Intent analysis node for deep intent understanding."""
    
    intent_agent = agent_registry.get_agent(AgentType.INTENT_ANALYSIS)
    if intent_agent:
        return await intent_agent.execute_with_metrics(state, config)
    else:
        return {"messages": [AIMessage(content="Intent analysis service unavailable.")]}


async def search_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Search agent node for deal and business discovery."""
    
    search_agent = agent_registry.get_agent(AgentType.SEARCH)
    if search_agent:
        return await search_agent.execute_with_metrics(state, config)
    else:
        return {"messages": [AIMessage(content="Search service unavailable.")]}


async def booking_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Booking agent node for booking lifecycle management."""
    
    booking_agent = agent_registry.get_agent(AgentType.BOOKING)
    if booking_agent:
        return await booking_agent.execute_with_metrics(state, config)
    else:
        return {"messages": [AIMessage(content="Booking service unavailable.")]}


async def communication_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Communication agent node for multi-channel messaging."""
    
    communication_agent = agent_registry.get_agent(AgentType.COMMUNICATION)
    if communication_agent:
        return await communication_agent.execute_with_metrics(state, config)
    else:
        return {"messages": [AIMessage(content="Communication service unavailable.")]}


async def user_management_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """User management agent node for profile and preferences."""
    
    user_agent = agent_registry.get_agent(AgentType.USER_MANAGEMENT)
    if user_agent:
        return await user_agent.execute_with_metrics(state, config)
    else:
        return {"messages": [AIMessage(content="User management service unavailable.")]}


async def error_handling_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Error handling agent node for error recovery."""
    
    error_agent = agent_registry.get_agent(AgentType.ERROR_HANDLING)
    if error_agent:
        return await error_agent.execute_with_metrics(state, config)
    else:
        return {"messages": [AIMessage(content="Error handling service unavailable.")]}


async def quality_assurance_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Quality assurance agent node for response validation."""
    
    qa_agent = agent_registry.get_agent(AgentType.QUALITY_ASSURANCE)
    if qa_agent:
        return await qa_agent.execute_with_metrics(state, config)
    else:
        return {"messages": [AIMessage(content="Quality assurance service unavailable.")]}


def should_continue_conversation(state: CatchUpState) -> str:
    """Determine if the conversation should continue or end."""
    
    messages = state.get("messages", [])
    if not messages:
        return END
    
    last_message = messages[-1]
    
    # Check if it's an error that needs handling
    if hasattr(last_message, 'additional_kwargs'):
        metadata = last_message.additional_kwargs.get('orchestrator_metadata', {})
        if 'error' in str(last_message.content).lower():
            return "error_handling"
    
    # Check for goodbye intents
    if isinstance(last_message, AIMessage):
        content = last_message.content.lower()
        if any(word in content for word in ['goodbye', 'bye', 'arrivederci', 'ciao']):
            return END
    
    # Continue conversation by default
    return END


def route_to_specialized_agent(state: CatchUpState) -> str:
    """Route to specialized agents based on orchestrator decision."""
    
    messages = state.get("messages", [])
    if not messages:
        return END
    
    last_message = messages[-1]
    
    # Check orchestrator metadata for routing decisions
    if hasattr(last_message, 'additional_kwargs'):
        metadata = last_message.additional_kwargs.get('orchestrator_metadata', {})
        intent = metadata.get('intent')
        
        # Route based on intent
        if intent in ['find_service', 'search_deals', 'search_businesses']:
            return "search"
        elif intent in ['book_service', 'view_bookings', 'booking_management']:
            return "booking"
        elif intent in ['send_email', 'send_whatsapp', 'communication']:
            return "communication"
        elif intent in ['user_profile', 'update_profile', 'user_preferences']:
            return "user_management"
        elif intent == 'error_recovery':
            return "error_handling"
    
    # Default to ending conversation
    return END


# Create the agentic graph
agentic_graph = (
    StateGraph(CatchUpState, config_schema=Configuration)
    
    # Add all agent nodes
    .add_node("orchestrator", orchestrator_node)
    .add_node("intent_analysis", intent_analysis_node)
    .add_node("search", search_node)
    .add_node("booking", booking_node)
    .add_node("communication", communication_node)
    .add_node("user_management", user_management_node)
    .add_node("error_handling", error_handling_node)
    .add_node("quality_assurance", quality_assurance_node)
    
    # Define the main flow
    .add_edge(START, "orchestrator")
    
    # Orchestrator handles all routing internally
    .add_conditional_edges(
        "orchestrator", 
        should_continue_conversation,
        ["error_handling", END]
    )
    
    # Error handling can route back to orchestrator or end
    .add_conditional_edges(
        "error_handling",
        lambda state: END,  # For now, end after error handling
        [END]
    )
    
    # Compile the graph
    .compile()
)


# Backward compatibility: create an alias for the original graph
graph = agentic_graph


# Alternative simplified graph for direct agent access (for testing)
def create_simple_agentic_graph():
    """Create a simplified version for testing individual agents."""
    
    return (
        StateGraph(CatchUpState, config_schema=Configuration)
        .add_node("intent_analysis", intent_analysis_node)
        .add_node("search", search_node)
        .add_node("booking", booking_node)
        .add_node("communication", communication_node)
        .add_node("user_management", user_management_node)
        .add_node("error_handling", error_handling_node)
        .add_node("quality_assurance", quality_assurance_node)
        
        .add_edge(START, "intent_analysis")
        .add_conditional_edges(
            "intent_analysis",
            route_to_specialized_agent,
            ["search", "booking", "communication", "user_management", "error_handling", END]
        )
        .add_edge("search", END)
        .add_edge("booking", END)
        .add_edge("communication", END)
        .add_edge("user_management", END)
        .add_edge("error_handling", END)
        .add_edge("quality_assurance", END)
        
        .compile()
    )


# Export both graphs
simple_graph = create_simple_agentic_graph()


# Utility function to get agent metrics
def get_system_metrics():
    """Get metrics for all agents in the system."""
    
    metrics = {}
    for agent in agent_registry.get_all_agents():
        metrics[agent.name] = {
            "type": agent.agent_type.value,
            "status": agent.status.value,
            "metrics": {
                "total_requests": agent.metrics.total_requests,
                "successful_requests": agent.metrics.successful_requests,
                "failed_requests": agent.metrics.failed_requests,
                "avg_execution_time": agent.metrics.avg_execution_time,
                "error_rate": agent.metrics.error_rate
            }
        }
    
    return metrics


# Utility function to reset all agent metrics
def reset_system_metrics():
    """Reset metrics for all agents."""
    
    for agent in agent_registry.get_all_agents():
        agent.reset_metrics()


# Health check function
async def health_check():
    """Perform a health check on all agents."""
    
    health_status = {
        "overall_status": "healthy",
        "agents": {},
        "issues": []
    }
    
    for agent in agent_registry.get_all_agents():
        agent_health = {
            "status": agent.status.value,
            "error_rate": agent.metrics.error_rate,
            "last_execution": agent.metrics.last_execution
        }
        
        # Check if agent is healthy
        if agent.metrics.error_rate > 0.5:  # More than 50% error rate
            agent_health["status"] = "unhealthy"
            health_status["issues"].append(f"{agent.name} has high error rate: {agent.metrics.error_rate:.2%}")
        
        health_status["agents"][agent.name] = agent_health
    
    # Set overall status
    if health_status["issues"]:
        health_status["overall_status"] = "degraded" if len(health_status["issues"]) < 3 else "unhealthy"
    
    return health_status
