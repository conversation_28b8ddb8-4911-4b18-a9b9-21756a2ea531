"""LangGraph agentic chatbot graph with orchestrator and specialized agents.

An advanced chatbot that uses an agentic architecture with specialized agents
for different domains (search, booking, communication, user management).
"""

from __future__ import annotations

import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

# Import the agentic graph implementation
from agent.agentic_graph import (
    agentic_graph,
    simple_graph,
    get_system_metrics,
    reset_system_metrics,
    health_check,
    Configuration
)
from agent.state import CatchUpState


# Use the agentic graph as the main graph
graph = agentic_graph

# Export additional utilities
__all__ = [
    "graph",
    "agentic_graph",
    "simple_graph",
    "Configuration",
    "CatchUpState",
    "get_system_metrics",
    "reset_system_metrics",
    "health_check"
]


# Backward compatibility functions for existing code
def should_route_to_tools(state: CatchUpState) -> str:
    """Legacy function for backward compatibility."""
    # This function is no longer used in the agentic architecture
    # but kept for compatibility with existing code
    return "__end__"


# Legacy imports for backward compatibility
try:
    from agent.nodes import call_model, intent_node
    from agent.nodes.enhanced_tools_node import enhanced_tools_node
except ImportError:
    # These are no longer required in the agentic architecture
    pass