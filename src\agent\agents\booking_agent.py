"""Booking Agent for booking lifecycle management."""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext
from agent.state import CatchUpState
from agent.tools.tool_manager import ToolCategory


class BookingAgent(BaseAgent):
    """Agent specialized in booking lifecycle management."""
    
    def __init__(self):
        super().__init__(AgentType.BOOKING, "Booking Agent")
        self.required_tool_categories = {
            ToolCategory.BOOKING
        }
        self.booking_validations = self._initialize_booking_validations()
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Process booking requests with validation and lifecycle management."""
        
        # Load tools if not already loaded
        if not self._tools:
            await self.load_tools()
        
        # Determine booking operation type
        operation = self._determine_booking_operation(state, context)
        
        # Execute booking operation
        result = await self._execute_booking_operation(operation, state, config, context)
        
        return result
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Check if this agent can handle booking-related intents."""
        booking_intents = [
            "book_service", "create_booking", "view_bookings", 
            "modify_booking", "cancel_booking", "booking_status",
            "booking_confirmation", "booking_management"
        ]
        return intent in booking_intents
    
    def _determine_booking_operation(self, state: CatchUpState, context: Optional[AgentContext]) -> str:
        """Determine the type of booking operation requested."""
        
        messages = state.get("messages", [])
        if not messages:
            return "view_bookings"
        
        last_message = messages[-1]
        message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
        message_lower = message_content.lower()
        
        # Check for specific booking operations
        if any(word in message_lower for word in ["prenota", "book", "create", "nuovo", "new"]):
            return "create_booking"
        elif any(word in message_lower for word in ["modifica", "modify", "change", "update"]):
            return "modify_booking"
        elif any(word in message_lower for word in ["cancella", "cancel", "delete", "remove"]):
            return "cancel_booking"
        elif any(word in message_lower for word in ["stato", "status", "conferma", "confirmation"]):
            return "booking_status"
        elif any(word in message_lower for word in ["mie prenotazioni", "my bookings", "list", "show"]):
            return "view_bookings"
        else:
            return "view_bookings"  # Default operation
    
    async def _execute_booking_operation(
        self, 
        operation: str, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext]
    ) -> Dict[str, Any]:
        """Execute the specific booking operation."""
        
        try:
            if operation == "create_booking":
                return await self._create_booking(state, config, context)
            elif operation == "modify_booking":
                return await self._modify_booking(state, config, context)
            elif operation == "cancel_booking":
                return await self._cancel_booking(state, config, context)
            elif operation == "booking_status":
                return await self._get_booking_status(state, config, context)
            elif operation == "view_bookings":
                return await self._view_bookings(state, config, context)
            else:
                return {"messages": [AIMessage(content="I'm not sure what booking operation you'd like to perform. Could you please clarify?")]}
                
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I encountered an error while processing your booking request: {str(e)}")],
                "booking_error": str(e)
            }
    
    async def _create_booking(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Create a new booking."""
        
        # Extract booking parameters
        booking_params = self._extract_booking_parameters(state, context)
        
        # Validate booking parameters
        validation_result = self._validate_booking_parameters(booking_params)
        if not validation_result["valid"]:
            return {
                "messages": [AIMessage(content=f"I need some additional information to create your booking: {validation_result['message']}")],
                "validation_error": validation_result["message"]
            }
        
        # Check availability
        availability_check = await self._check_availability(booking_params, state, config)
        if not availability_check["available"]:
            return {
                "messages": [AIMessage(content=f"Unfortunately, this time slot is not available. {availability_check.get('message', '')}")],
                "availability_error": availability_check.get("message", "")
            }
        
        # Create the booking
        create_booking_tool = self._get_tool_by_name("create_booking")
        if not create_booking_tool:
            return {"messages": [AIMessage(content="Booking service is currently unavailable. Please try again later.")]}
        
        try:
            booking_result = await create_booking_tool.ainvoke(booking_params)
            
            # Generate confirmation message
            confirmation_message = self._generate_booking_confirmation(booking_result, booking_params)
            
            return {
                "messages": [AIMessage(content=confirmation_message)],
                "booking_created": booking_result,
                "booking_id": booking_result.get("booking_id") if isinstance(booking_result, dict) else None
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I couldn't complete your booking due to a technical issue: {str(e)}")],
                "booking_error": str(e)
            }
    
    async def _modify_booking(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Modify an existing booking."""
        
        # Extract booking ID and modification parameters
        booking_id = self._extract_booking_id(state, context)
        if not booking_id:
            return {
                "messages": [AIMessage(content="I need your booking ID to modify your reservation. Could you please provide it?")],
                "missing_booking_id": True
            }
        
        # Get current booking details
        get_booking_tool = self._get_tool_by_name("get_booking")
        if not get_booking_tool:
            return {"messages": [AIMessage(content="Booking service is currently unavailable.")]}
        
        try:
            current_booking = await get_booking_tool.ainvoke({"booking_id": booking_id})
            
            # Extract modification parameters
            modifications = self._extract_modification_parameters(state, context)
            
            # Validate modifications
            if not modifications:
                return {
                    "messages": [AIMessage(content="What would you like to modify about your booking? (date, time, or other details)")],
                    "needs_modification_details": True
                }
            
            # Apply modifications (simplified - would use update_booking tool in real implementation)
            update_booking_tool = self._get_tool_by_name("update_booking")
            if update_booking_tool:
                updated_booking = await update_booking_tool.ainvoke({
                    "booking_id": booking_id,
                    **modifications
                })
                
                return {
                    "messages": [AIMessage(content=f"Your booking has been successfully updated! New details: {self._format_booking_details(updated_booking)}")],
                    "booking_updated": updated_booking
                }
            else:
                return {
                    "messages": [AIMessage(content="I can see your current booking details, but the modification service is temporarily unavailable. Please contact support for assistance.")],
                    "current_booking": current_booking
                }
                
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I couldn't retrieve your booking details: {str(e)}")],
                "booking_error": str(e)
            }
    
    async def _cancel_booking(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Cancel an existing booking."""
        
        booking_id = self._extract_booking_id(state, context)
        if not booking_id:
            return {
                "messages": [AIMessage(content="I need your booking ID to cancel your reservation. Could you please provide it?")],
                "missing_booking_id": True
            }
        
        # Get booking details first
        get_booking_tool = self._get_tool_by_name("get_booking")
        if get_booking_tool:
            try:
                booking_details = await get_booking_tool.ainvoke({"booking_id": booking_id})
                
                # Check cancellation policy
                cancellation_allowed = self._check_cancellation_policy(booking_details)
                if not cancellation_allowed["allowed"]:
                    return {
                        "messages": [AIMessage(content=f"I'm sorry, but this booking cannot be cancelled: {cancellation_allowed['reason']}")],
                        "cancellation_denied": cancellation_allowed["reason"]
                    }
                
                # Proceed with cancellation (simplified - would use actual cancellation tool)
                return {
                    "messages": [AIMessage(content=f"Your booking {booking_id} has been successfully cancelled. You should receive a confirmation email shortly.")],
                    "booking_cancelled": booking_id,
                    "cancellation_details": booking_details
                }
                
            except Exception as e:
                return {
                    "messages": [AIMessage(content=f"I couldn't process your cancellation request: {str(e)}")],
                    "cancellation_error": str(e)
                }
        
        return {"messages": [AIMessage(content="Booking service is currently unavailable.")]}
    
    async def _get_booking_status(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Get the status of a specific booking."""
        
        booking_id = self._extract_booking_id(state, context)
        if not booking_id:
            return {
                "messages": [AIMessage(content="I need your booking ID to check the status. Could you please provide it?")],
                "missing_booking_id": True
            }
        
        get_booking_tool = self._get_tool_by_name("get_booking")
        if get_booking_tool:
            try:
                booking_details = await get_booking_tool.ainvoke({"booking_id": booking_id})
                
                status_message = self._format_booking_status(booking_details)
                
                return {
                    "messages": [AIMessage(content=status_message)],
                    "booking_details": booking_details
                }
                
            except Exception as e:
                return {
                    "messages": [AIMessage(content=f"I couldn't retrieve your booking status: {str(e)}")],
                    "status_error": str(e)
                }
        
        return {"messages": [AIMessage(content="Booking service is currently unavailable.")]}
    
    async def _view_bookings(self, state: CatchUpState, config: RunnableConfig, context: Optional[AgentContext]) -> Dict[str, Any]:
        """View all bookings for the user."""
        
        user_context = state.get("user_context", {})
        user_id = user_context.get("user_id")
        
        if not user_id:
            return {
                "messages": [AIMessage(content="I need to identify you to show your bookings. Please make sure you're logged in.")],
                "missing_user_id": True
            }
        
        # Get user bookings (simplified - would use actual get_user_bookings tool)
        get_booking_tool = self._get_tool_by_name("get_booking")
        if get_booking_tool:
            try:
                # In real implementation, would have a get_user_bookings tool
                # For now, simulate getting user bookings
                bookings_message = "Here are your current bookings:\n\n"
                bookings_message += "📅 **Upcoming Bookings:**\n"
                bookings_message += "• No upcoming bookings found\n\n"
                bookings_message += "📋 **Recent Bookings:**\n"
                bookings_message += "• No recent bookings found\n\n"
                bookings_message += "To make a new booking, just tell me what service you're looking for!"
                
                return {
                    "messages": [AIMessage(content=bookings_message)],
                    "user_bookings": []
                }
                
            except Exception as e:
                return {
                    "messages": [AIMessage(content=f"I couldn't retrieve your bookings: {str(e)}")],
                    "bookings_error": str(e)
                }
        
        return {"messages": [AIMessage(content="Booking service is currently unavailable.")]}
    
    def _extract_booking_parameters(self, state: CatchUpState, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Extract booking parameters from state and context."""
        
        # Get entities from intent analysis
        entities = {}
        if context and context.metadata:
            entities = context.metadata.get("entities", {})
        
        # Extract from messages
        messages = state.get("messages", [])
        message_content = ""
        if messages:
            last_message = messages[-1]
            message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
        
        # Get user context
        user_context = state.get("user_context", {})
        
        return {
            "user_id": user_context.get("user_id"),
            "deal_id": self._extract_deal_id(message_content, entities),
            "preferred_time": entities.get("time", []),
            "preferred_date": self._extract_date_preference(message_content),
            "special_requests": self._extract_special_requests(message_content),
            "contact_info": {
                "email": user_context.get("email_address"),
                "phone": user_context.get("phone_number")
            }
        }
    
    def _validate_booking_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate booking parameters."""
        
        if not params.get("user_id"):
            return {"valid": False, "message": "User identification is required"}
        
        if not params.get("deal_id"):
            return {"valid": False, "message": "Please specify which service you'd like to book"}
        
        if not params.get("preferred_time") and not params.get("preferred_date"):
            return {"valid": False, "message": "Please specify your preferred date and time"}
        
        return {"valid": True, "message": "All required parameters provided"}
    
    async def _check_availability(self, params: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Check availability for the requested booking."""
        
        # Simplified availability check
        # In real implementation, would check against business calendar
        
        return {
            "available": True,
            "message": "Time slot is available",
            "alternative_times": []
        }
    
    def _generate_booking_confirmation(self, booking_result: Any, params: Dict[str, Any]) -> str:
        """Generate a booking confirmation message."""
        
        confirmation = "🎉 **Booking Confirmed!**\n\n"
        
        if isinstance(booking_result, dict):
            booking_id = booking_result.get("booking_id", "Unknown")
            confirmation += f"📋 **Booking ID:** {booking_id}\n"
            
            service_name = booking_result.get("service_name", "Service")
            confirmation += f"🎯 **Service:** {service_name}\n"
            
            booking_date = booking_result.get("date", "TBD")
            booking_time = booking_result.get("time", "TBD")
            confirmation += f"📅 **Date & Time:** {booking_date} at {booking_time}\n\n"
        
        confirmation += "✅ You'll receive a confirmation email shortly\n"
        confirmation += "📱 You can manage your booking anytime through the app\n"
        confirmation += "❓ Need to make changes? Just let me know!"
        
        return confirmation
    
    def _extract_booking_id(self, state: CatchUpState, context: Optional[AgentContext]) -> Optional[str]:
        """Extract booking ID from messages or context."""
        
        messages = state.get("messages", [])
        if messages:
            last_message = messages[-1]
            message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
            
            # Look for booking ID patterns
            import re
            booking_id_match = re.search(r'booking[_\s]*id[:\s]*([a-zA-Z0-9\-]+)', message_content.lower())
            if booking_id_match:
                return booking_id_match.group(1)
            
            # Look for simple ID patterns
            id_match = re.search(r'\b([a-zA-Z0-9]{8,})\b', message_content)
            if id_match:
                return id_match.group(1)
        
        return None
    
    def _extract_modification_parameters(self, state: CatchUpState, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Extract modification parameters from messages."""
        
        messages = state.get("messages", [])
        if not messages:
            return {}
        
        last_message = messages[-1]
        message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
        message_lower = message_content.lower()
        
        modifications = {}
        
        # Check for date changes
        if any(word in message_lower for word in ["data", "date", "giorno", "day"]):
            modifications["new_date"] = self._extract_date_preference(message_content)
        
        # Check for time changes
        if any(word in message_lower for word in ["ora", "time", "orario", "hour"]):
            modifications["new_time"] = self._extract_time_preference(message_content)
        
        return modifications
    
    def _check_cancellation_policy(self, booking_details: Any) -> Dict[str, Any]:
        """Check if booking can be cancelled according to policy."""
        
        # Simplified cancellation policy check
        # In real implementation, would check business-specific policies
        
        return {
            "allowed": True,
            "reason": "Cancellation allowed up to 24 hours before appointment"
        }
    
    def _format_booking_status(self, booking_details: Any) -> str:
        """Format booking status message."""
        
        if isinstance(booking_details, dict):
            status = booking_details.get("status", "Unknown")
            service = booking_details.get("service_name", "Service")
            date = booking_details.get("date", "TBD")
            time = booking_details.get("time", "TBD")
            
            return f"📋 **Booking Status:** {status}\n🎯 **Service:** {service}\n📅 **Date & Time:** {date} at {time}"
        
        return "I found your booking, but couldn't retrieve all the details. Please contact support for complete information."
    
    def _format_booking_details(self, booking: Any) -> str:
        """Format booking details for display."""
        
        if isinstance(booking, dict):
            details = []
            if booking.get("service_name"):
                details.append(f"Service: {booking['service_name']}")
            if booking.get("date"):
                details.append(f"Date: {booking['date']}")
            if booking.get("time"):
                details.append(f"Time: {booking['time']}")
            
            return ", ".join(details) if details else "Updated successfully"
        
        return "Updated successfully"
    
    def _extract_deal_id(self, message_content: str, entities: Dict[str, Any]) -> Optional[str]:
        """Extract deal ID from message content."""
        # Simplified extraction - would be more sophisticated in real implementation
        import re
        deal_match = re.search(r'deal[_\s]*id[:\s]*([a-zA-Z0-9\-]+)', message_content.lower())
        return deal_match.group(1) if deal_match else None
    
    def _extract_date_preference(self, message_content: str) -> Optional[str]:
        """Extract date preference from message."""
        # Simplified date extraction
        import re
        date_match = re.search(r'\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}', message_content)
        return date_match.group(0) if date_match else None
    
    def _extract_time_preference(self, message_content: str) -> Optional[str]:
        """Extract time preference from message."""
        import re
        time_match = re.search(r'\d{1,2}:\d{2}', message_content)
        return time_match.group(0) if time_match else None
    
    def _extract_special_requests(self, message_content: str) -> Optional[str]:
        """Extract special requests from message."""
        # Look for special request indicators
        message_lower = message_content.lower()
        if any(word in message_lower for word in ["note", "special", "request", "prefer"]):
            return message_content
        return None
    
    def _get_tool_by_name(self, tool_name: str):
        """Get a tool by name from loaded tools."""
        if not self._tools:
            return None
        
        for tool in self._tools:
            if hasattr(tool, 'name') and tool.name == tool_name:
                return tool
        return None
    
    def _initialize_booking_validations(self) -> Dict[str, Any]:
        """Initialize booking validation rules."""
        return {
            "required_fields": ["user_id", "deal_id"],
            "optional_fields": ["preferred_time", "preferred_date", "special_requests"],
            "validation_rules": {
                "advance_booking_hours": 2,  # Minimum 2 hours advance booking
                "max_advance_days": 90,      # Maximum 90 days advance booking
                "business_hours": {"start": "09:00", "end": "18:00"}
            }
        }
