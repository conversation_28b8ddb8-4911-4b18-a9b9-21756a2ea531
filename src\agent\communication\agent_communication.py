"""Agent-to-agent communication protocols and utilities."""

from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from enum import Enum
import asyncio
import time
import uuid

from agent.agents.base_agent import AgentType, AgentContext


class MessageType(Enum):
    """Types of inter-agent messages."""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"
    HEARTBEAT = "heartbeat"


class MessagePriority(Enum):
    """Message priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class AgentMessage:
    """Message structure for agent-to-agent communication."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender: AgentType = None
    recipient: AgentType = None
    message_type: MessageType = MessageType.REQUEST
    priority: MessagePriority = MessagePriority.NORMAL
    payload: Dict[str, Any] = field(default_factory=dict)
    context: Optional[AgentContext] = None
    timestamp: float = field(default_factory=time.time)
    correlation_id: Optional[str] = None  # For request-response correlation
    expires_at: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class CommunicationStats:
    """Statistics for agent communication."""
    messages_sent: int = 0
    messages_received: int = 0
    messages_failed: int = 0
    avg_response_time: float = 0.0
    total_response_time: float = 0.0
    successful_responses: int = 0


class AgentCommunicationBus:
    """Central communication bus for agent-to-agent messaging."""
    
    def __init__(self):
        self.message_queue: Dict[AgentType, List[AgentMessage]] = {}
        self.subscribers: Dict[MessageType, List[Callable]] = {}
        self.stats: Dict[AgentType, CommunicationStats] = {}
        self.pending_requests: Dict[str, AgentMessage] = {}
        self.response_handlers: Dict[str, Callable] = {}
        self._running = False
        self._processor_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """Start the communication bus."""
        if not self._running:
            self._running = True
            self._processor_task = asyncio.create_task(self._process_messages())
    
    async def stop(self):
        """Stop the communication bus."""
        self._running = False
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass
    
    def register_agent(self, agent_type: AgentType):
        """Register an agent with the communication bus."""
        if agent_type not in self.message_queue:
            self.message_queue[agent_type] = []
            self.stats[agent_type] = CommunicationStats()
    
    def subscribe(self, message_type: MessageType, handler: Callable):
        """Subscribe to specific message types."""
        if message_type not in self.subscribers:
            self.subscribers[message_type] = []
        self.subscribers[message_type].append(handler)
    
    async def send_message(self, message: AgentMessage) -> bool:
        """Send a message to another agent."""
        try:
            # Validate message
            if not self._validate_message(message):
                return False
            
            # Add to recipient's queue
            if message.recipient not in self.message_queue:
                self.register_agent(message.recipient)
            
            # Set expiration if not set
            if message.expires_at is None:
                message.expires_at = time.time() + 300  # 5 minutes default
            
            # Add to queue based on priority
            self._add_to_queue(message)
            
            # Update stats
            self.stats[message.sender].messages_sent += 1
            
            # If it's a request, store for correlation
            if message.message_type == MessageType.REQUEST:
                self.pending_requests[message.id] = message
            
            return True
            
        except Exception as e:
            print(f"Failed to send message: {e}")
            if message.sender in self.stats:
                self.stats[message.sender].messages_failed += 1
            return False
    
    async def send_request(
        self, 
        sender: AgentType, 
        recipient: AgentType, 
        payload: Dict[str, Any],
        timeout: float = 30.0
    ) -> Optional[AgentMessage]:
        """Send a request and wait for response."""
        
        request = AgentMessage(
            sender=sender,
            recipient=recipient,
            message_type=MessageType.REQUEST,
            payload=payload,
            correlation_id=str(uuid.uuid4())
        )
        
        # Create response future
        response_future = asyncio.Future()
        self.response_handlers[request.correlation_id] = response_future.set_result
        
        # Send request
        success = await self.send_message(request)
        if not success:
            return None
        
        try:
            # Wait for response
            response = await asyncio.wait_for(response_future, timeout=timeout)
            return response
        except asyncio.TimeoutError:
            # Clean up
            self.response_handlers.pop(request.correlation_id, None)
            self.pending_requests.pop(request.id, None)
            return None
    
    async def send_response(
        self, 
        original_request: AgentMessage, 
        payload: Dict[str, Any]
    ) -> bool:
        """Send a response to a request."""
        
        response = AgentMessage(
            sender=original_request.recipient,
            recipient=original_request.sender,
            message_type=MessageType.RESPONSE,
            payload=payload,
            correlation_id=original_request.correlation_id
        )
        
        return await self.send_message(response)
    
    async def broadcast(
        self, 
        sender: AgentType, 
        message_type: MessageType, 
        payload: Dict[str, Any]
    ):
        """Broadcast a message to all agents."""
        
        for agent_type in self.message_queue.keys():
            if agent_type != sender:
                message = AgentMessage(
                    sender=sender,
                    recipient=agent_type,
                    message_type=message_type,
                    payload=payload
                )
                await self.send_message(message)
    
    async def get_messages(self, agent_type: AgentType) -> List[AgentMessage]:
        """Get pending messages for an agent."""
        
        if agent_type not in self.message_queue:
            return []
        
        messages = self.message_queue[agent_type].copy()
        self.message_queue[agent_type].clear()
        
        # Update stats
        self.stats[agent_type].messages_received += len(messages)
        
        return messages
    
    def get_stats(self, agent_type: Optional[AgentType] = None) -> Dict[str, Any]:
        """Get communication statistics."""
        
        if agent_type:
            return {
                "agent": agent_type.value,
                "stats": self.stats.get(agent_type, CommunicationStats())
            }
        
        return {
            "total_agents": len(self.stats),
            "agent_stats": {
                agent.value: stats for agent, stats in self.stats.items()
            },
            "pending_requests": len(self.pending_requests),
            "total_queued_messages": sum(len(queue) for queue in self.message_queue.values())
        }
    
    async def _process_messages(self):
        """Background task to process messages."""
        
        while self._running:
            try:
                # Process expired messages
                await self._cleanup_expired_messages()
                
                # Process responses
                await self._process_responses()
                
                # Notify subscribers
                await self._notify_subscribers()
                
                # Sleep briefly
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"Error in message processor: {e}")
                await asyncio.sleep(1)
    
    async def _cleanup_expired_messages(self):
        """Remove expired messages."""
        
        current_time = time.time()
        
        # Clean up pending requests
        expired_requests = [
            msg_id for msg_id, msg in self.pending_requests.items()
            if msg.expires_at and msg.expires_at < current_time
        ]
        
        for msg_id in expired_requests:
            self.pending_requests.pop(msg_id, None)
        
        # Clean up message queues
        for agent_type, queue in self.message_queue.items():
            self.message_queue[agent_type] = [
                msg for msg in queue
                if not msg.expires_at or msg.expires_at >= current_time
            ]
    
    async def _process_responses(self):
        """Process response messages."""
        
        for agent_type, queue in self.message_queue.items():
            responses = [msg for msg in queue if msg.message_type == MessageType.RESPONSE]
            
            for response in responses:
                if response.correlation_id in self.response_handlers:
                    handler = self.response_handlers.pop(response.correlation_id)
                    handler(response)
                    
                    # Update response time stats
                    if response.sender in self.stats:
                        original_request = self.pending_requests.get(response.correlation_id)
                        if original_request:
                            response_time = time.time() - original_request.timestamp
                            stats = self.stats[response.sender]
                            stats.successful_responses += 1
                            stats.total_response_time += response_time
                            stats.avg_response_time = (
                                stats.total_response_time / stats.successful_responses
                            )
                    
                    # Remove from queue
                    queue.remove(response)
    
    async def _notify_subscribers(self):
        """Notify message type subscribers."""
        
        for message_type, handlers in self.subscribers.items():
            # Find messages of this type across all queues
            matching_messages = []
            for queue in self.message_queue.values():
                matching_messages.extend([
                    msg for msg in queue if msg.message_type == message_type
                ])
            
            # Notify handlers
            for message in matching_messages:
                for handler in handlers:
                    try:
                        await handler(message)
                    except Exception as e:
                        print(f"Error in message handler: {e}")
    
    def _validate_message(self, message: AgentMessage) -> bool:
        """Validate message structure."""
        
        if not message.sender or not message.recipient:
            return False
        
        if message.sender == message.recipient:
            return False  # No self-messaging
        
        if not message.message_type:
            return False
        
        return True
    
    def _add_to_queue(self, message: AgentMessage):
        """Add message to queue based on priority."""
        
        queue = self.message_queue[message.recipient]
        
        # Insert based on priority (higher priority first)
        inserted = False
        for i, existing_msg in enumerate(queue):
            if message.priority.value > existing_msg.priority.value:
                queue.insert(i, message)
                inserted = True
                break
        
        if not inserted:
            queue.append(message)


# Global communication bus instance
communication_bus = AgentCommunicationBus()


# Utility functions for easy agent communication
async def send_agent_request(
    sender: AgentType,
    recipient: AgentType,
    payload: Dict[str, Any],
    timeout: float = 30.0
) -> Optional[AgentMessage]:
    """Utility function to send a request between agents."""
    return await communication_bus.send_request(sender, recipient, payload, timeout)


async def send_agent_notification(
    sender: AgentType,
    recipient: AgentType,
    payload: Dict[str, Any]
) -> bool:
    """Utility function to send a notification between agents."""
    message = AgentMessage(
        sender=sender,
        recipient=recipient,
        message_type=MessageType.NOTIFICATION,
        payload=payload
    )
    return await communication_bus.send_message(message)


async def broadcast_to_agents(
    sender: AgentType,
    payload: Dict[str, Any],
    message_type: MessageType = MessageType.NOTIFICATION
):
    """Utility function to broadcast to all agents."""
    await communication_bus.broadcast(sender, message_type, payload)


# Context manager for communication bus
class CommunicationBusContext:
    """Context manager for the communication bus."""
    
    async def __aenter__(self):
        await communication_bus.start()
        return communication_bus
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await communication_bus.stop()


# Decorator for agent methods that need communication
def with_communication(func):
    """Decorator to ensure communication bus is available."""
    
    async def wrapper(*args, **kwargs):
        if not communication_bus._running:
            await communication_bus.start()
        return await func(*args, **kwargs)
    
    return wrapper
