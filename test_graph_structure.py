"""Test script to verify the graph structure and connections."""

import asyncio
from src.agent.graph import agentic_graph, orchestrator_graph, simple_graph
from src.agent.state import CatchUpState
from langchain_core.messages import HumanMessage


async def test_graph_structure():
    """Test the graph structure and basic functionality."""
    
    print("🧪 Testing Graph Structure")
    print("=" * 50)
    
    # Test state
    test_state = CatchUpState(
        messages=[HumanMessage(content="I'm looking for a restaurant in Rome")],
        user_context={
            "user_id": "test_user_123",
            "email_address": "<EMAIL>",
            "location": {"latitude": 41.9028, "longitude": 12.4964}
        },
        session_config={
            "session_id": "test_session_123"
        }
    )
    
    config = {
        "configurable": {
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful customer service assistant."
        }
    }
    
    # Test agentic graph
    print("\n1. Testing Agentic Graph (Intent → Domain Agents → QA)")
    try:
        result = await agentic_graph.ainvoke(test_state, config)
        print(f"   ✅ Agentic graph executed successfully")
        print(f"   📝 Response: {result['messages'][-1].content[:100]}...")
    except Exception as e:
        print(f"   ❌ Agentic graph failed: {e}")
    
    # Test orchestrator graph
    print("\n2. Testing Orchestrator Graph (Orchestrator handles all)")
    try:
        result = await orchestrator_graph.ainvoke(test_state, config)
        print(f"   ✅ Orchestrator graph executed successfully")
        print(f"   📝 Response: {result['messages'][-1].content[:100]}...")
    except Exception as e:
        print(f"   ❌ Orchestrator graph failed: {e}")
    
    # Test simple graph
    print("\n3. Testing Simple Graph (Direct agent access)")
    try:
        result = await simple_graph.ainvoke(test_state, config)
        print(f"   ✅ Simple graph executed successfully")
        print(f"   📝 Response: {result['messages'][-1].content[:100]}...")
    except Exception as e:
        print(f"   ❌ Simple graph failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Graph structure tests completed!")


def print_graph_info():
    """Print information about the graph structures."""
    
    print("\n📊 Graph Information")
    print("=" * 50)
    
    # Agentic graph info
    print("\n🔄 Agentic Graph:")
    print("   Flow: START → intent_analysis → [domain_agents] → quality_assurance → END")
    print("   Nodes:", list(agentic_graph.nodes.keys()))
    
    # Orchestrator graph info
    print("\n🎯 Orchestrator Graph:")
    print("   Flow: START → orchestrator → END")
    print("   Nodes:", list(orchestrator_graph.nodes.keys()))
    
    # Simple graph info
    print("\n🔧 Simple Graph:")
    print("   Flow: START → intent_analysis → [domain_agents] → END")
    print("   Nodes:", list(simple_graph.nodes.keys()))


if __name__ == "__main__":
    print_graph_info()
    asyncio.run(test_graph_structure())
