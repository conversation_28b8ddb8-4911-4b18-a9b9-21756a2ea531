"""Intent Analysis Agent for deep intent understanding and classification."""

import re
from typing import Dict, Any, Optional, List, Tuple
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext
from agent.state import CatchUpState
from shared import create_llm


class IntentAnalysisAgent(BaseAgent):
    """Agent specialized in deep intent understanding and entity extraction."""
    
    def __init__(self):
        super().__init__(AgentType.INTENT_ANALYSIS, "Intent Analysis Agent")
        self.intent_patterns = self._initialize_intent_patterns()
        self.entity_extractors = self._initialize_entity_extractors()
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Analyze user intent and extract entities."""
        
        messages = state.get("messages", [])
        if not messages:
            return {"intent_analysis": {"intent": "greetings", "confidence": 1.0}}
        
        last_message = messages[-1]
        user_input = last_message.content if hasattr(last_message, 'content') else str(last_message)
        
        # Perform intent analysis
        intent_result = await self._analyze_intent(user_input, state, config)
        
        # Extract entities
        entities = self._extract_entities(user_input)
        
        # Determine required tools
        required_tools = self._determine_required_tools(intent_result["intent"], entities)
        
        # Calculate confidence score
        confidence = self._calculate_confidence(intent_result, entities, user_input)
        
        analysis_result = {
            "intent": intent_result["intent"],
            "confidence": confidence,
            "entities": entities,
            "required_tools": required_tools,
            "analysis_details": {
                "user_input_length": len(user_input),
                "entities_found": len(entities),
                "pattern_matches": intent_result.get("pattern_matches", []),
                "llm_analysis": intent_result.get("llm_analysis", "")
            }
        }
        
        return {
            "intent_analysis": analysis_result,
            "messages": [AIMessage(content=f"Intent analyzed: {intent_result['intent']} (confidence: {confidence:.2f})")]
        }
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Intent analysis agent handles intent analysis requests."""
        return intent in ["analyze_intent", "extract_entities", "classify_request"]
    
    async def _analyze_intent(self, user_input: str, state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Analyze user intent using pattern matching and LLM."""
        
        # First, try pattern-based classification
        pattern_result = self._classify_with_patterns(user_input)
        
        # If pattern matching is confident, use it
        if pattern_result["confidence"] > 0.8:
            return pattern_result
        
        # Otherwise, use LLM for more sophisticated analysis
        llm_result = await self._classify_with_llm(user_input, state, config)
        
        # Combine results
        return {
            "intent": llm_result.get("intent", pattern_result["intent"]),
            "pattern_matches": pattern_result.get("matches", []),
            "llm_analysis": llm_result.get("analysis", ""),
            "confidence": max(pattern_result["confidence"], llm_result.get("confidence", 0.0))
        }
    
    def _classify_with_patterns(self, user_input: str) -> Dict[str, Any]:
        """Classify intent using predefined patterns."""
        
        user_input_lower = user_input.lower()
        matches = []
        best_intent = "generic_chat"
        best_confidence = 0.0
        
        for intent, patterns in self.intent_patterns.items():
            intent_matches = 0
            for pattern in patterns:
                if re.search(pattern, user_input_lower):
                    intent_matches += 1
                    matches.append({"intent": intent, "pattern": pattern})
            
            # Calculate confidence based on pattern matches
            if intent_matches > 0:
                confidence = min(intent_matches / len(patterns), 1.0)
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_intent = intent
        
        return {
            "intent": best_intent,
            "confidence": best_confidence,
            "matches": matches
        }
    
    async def _classify_with_llm(self, user_input: str, state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Use LLM for sophisticated intent classification."""
        
        configuration = config.get("configurable", {})
        model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")
        llm = create_llm(model_name)
        
        system_prompt = """You are an expert intent classifier for a marketplace customer service system.
        
Analyze the user's message and classify it into one of these intents:
- find_service: User wants to search for services or deals
- book_service: User wants to make a booking
- view_bookings: User wants to see their existing bookings
- ask_offer_info: User wants information about specific offers
- greetings: User is greeting or starting conversation
- goodbye: User is ending conversation
- request_help: User needs general help or support
- generic_chat: General conversation not related to services
- not_understood: Intent is unclear

Respond with a JSON object containing:
- intent: the classified intent
- confidence: confidence score (0.0 to 1.0)
- analysis: brief explanation of your classification

User message: "{user_input}"
""".format(user_input=user_input)
        
        try:
            response = await llm.ainvoke([SystemMessage(content=system_prompt)])
            
            # Parse LLM response (simplified - in production would use structured output)
            response_text = response.content
            
            # Extract intent from response (basic parsing)
            intent = self._extract_intent_from_response(response_text)
            confidence = self._extract_confidence_from_response(response_text)
            
            return {
                "intent": intent,
                "confidence": confidence,
                "analysis": response_text
            }
            
        except Exception as e:
            print(f"LLM intent classification failed: {e}")
            return {"intent": "generic_chat", "confidence": 0.3, "analysis": "LLM analysis failed"}
    
    def _extract_entities(self, user_input: str) -> Dict[str, List[str]]:
        """Extract entities from user input."""
        
        entities = {}
        
        for entity_type, extractor in self.entity_extractors.items():
            extracted = extractor(user_input)
            if extracted:
                entities[entity_type] = extracted
        
        return entities
    
    def _determine_required_tools(self, intent: str, entities: Dict[str, List[str]]) -> List[str]:
        """Determine which tools are required based on intent and entities."""
        
        tool_mapping = {
            "find_service": ["get_categories", "search_deals", "search_businesses"],
            "book_service": ["create_booking", "get_deals"],
            "view_bookings": ["get_booking"],
            "ask_offer_info": ["get_deals", "search_deals"],
            "greetings": [],
            "goodbye": [],
            "request_help": ["get_categories"],
            "generic_chat": [],
            "not_understood": ["get_categories"]
        }
        
        base_tools = tool_mapping.get(intent, [])
        
        # Add tools based on entities
        if "location" in entities:
            base_tools.extend(["search_businesses", "search_deals"])
        
        if "communication" in entities:
            base_tools.extend(["send_email", "send_whatsapp"])
        
        if "user_info" in entities:
            base_tools.extend(["get_user", "update_user"])
        
        return list(set(base_tools))  # Remove duplicates
    
    def _calculate_confidence(self, intent_result: Dict[str, Any], entities: Dict[str, List[str]], user_input: str) -> float:
        """Calculate overall confidence score."""
        
        base_confidence = intent_result.get("confidence", 0.0)
        
        # Boost confidence if entities are found
        entity_boost = min(len(entities) * 0.1, 0.3)
        
        # Boost confidence for longer, more detailed inputs
        length_boost = min(len(user_input) / 1000, 0.2)
        
        # Penalize very short inputs
        if len(user_input.strip()) < 5:
            base_confidence *= 0.5
        
        final_confidence = min(base_confidence + entity_boost + length_boost, 1.0)
        return round(final_confidence, 2)
    
    def _initialize_intent_patterns(self) -> Dict[str, List[str]]:
        """Initialize intent classification patterns."""
        return {
            "find_service": [
                r"cerco|cerca|trovare|ricerca|search|find",
                r"servizio|service|deal|offerta|sconto",
                r"dove posso|where can|come posso|how can",
                r"disponibile|available|aperto|open"
            ],
            "book_service": [
                r"prenot|book|reserv|appointment",
                r"voglio prenotare|want to book|need to book",
                r"disponibilità|availability|slot|orario"
            ],
            "view_bookings": [
                r"mie prenotazioni|my bookings|my reservations",
                r"prenotazione|booking|reservation",
                r"stato|status|conferma|confirmation"
            ],
            "ask_offer_info": [
                r"informazioni|info|details|dettagli",
                r"quanto costa|how much|prezzo|price",
                r"cosa include|what includes|cosa comprende"
            ],
            "greetings": [
                r"ciao|hello|hi|buongiorno|good morning|salve",
                r"come stai|how are you|come va"
            ],
            "goodbye": [
                r"arrivederci|goodbye|bye|ciao|grazie|thank you",
                r"basta|enough|fine|stop"
            ],
            "request_help": [
                r"aiuto|help|supporto|support|assistenza",
                r"non capisco|don't understand|confused",
                r"problema|problem|issue|errore|error"
            ]
        }
    
    def _initialize_entity_extractors(self) -> Dict[str, callable]:
        """Initialize entity extraction functions."""
        return {
            "location": self._extract_locations,
            "time": self._extract_time_expressions,
            "communication": self._extract_communication_preferences,
            "user_info": self._extract_user_info,
            "categories": self._extract_categories
        }
    
    def _extract_locations(self, text: str) -> List[str]:
        """Extract location entities."""
        location_patterns = [
            r"roma|milan[oa]|napoli|torino|firenze|bologna|venezia",
            r"centro|duomo|stazione|aeroporto",
            r"zona \w+|quartiere \w+"
        ]
        
        locations = []
        for pattern in location_patterns:
            matches = re.findall(pattern, text.lower())
            locations.extend(matches)
        
        return list(set(locations))
    
    def _extract_time_expressions(self, text: str) -> List[str]:
        """Extract time-related entities."""
        time_patterns = [
            r"\d{1,2}:\d{2}",  # Time format
            r"oggi|domani|dopodomani|ieri",  # Relative days
            r"lunedì|martedì|mercoledì|giovedì|venerdì|sabato|domenica",  # Days
            r"mattina|pomeriggio|sera|notte",  # Time periods
            r"\d{1,2} \w+",  # Date expressions
        ]
        
        times = []
        for pattern in time_patterns:
            matches = re.findall(pattern, text.lower())
            times.extend(matches)
        
        return list(set(times))
    
    def _extract_communication_preferences(self, text: str) -> List[str]:
        """Extract communication preferences."""
        comm_patterns = [
            r"email|mail|e-mail",
            r"whatsapp|wa|messaggio",
            r"sms|telefono|chiamata"
        ]
        
        prefs = []
        for pattern in comm_patterns:
            if re.search(pattern, text.lower()):
                prefs.append(pattern.split('|')[0])  # Take first variant
        
        return prefs
    
    def _extract_user_info(self, text: str) -> List[str]:
        """Extract user information references."""
        user_patterns = [
            r"mio profilo|my profile|account",
            r"miei dati|my data|informazioni personali",
            r"preferenze|preferences|settings"
        ]
        
        info = []
        for pattern in user_patterns:
            if re.search(pattern, text.lower()):
                info.append("profile_reference")
        
        return info
    
    def _extract_categories(self, text: str) -> List[str]:
        """Extract service category mentions."""
        category_patterns = [
            r"ristorante|restaurant|cibo|food",
            r"bellezza|beauty|parrucchiere|estetista",
            r"sport|fitness|palestra|gym",
            r"benessere|wellness|spa|massaggio",
            r"auto|car|meccanico|automotive"
        ]
        
        categories = []
        for pattern in category_patterns:
            if re.search(pattern, text.lower()):
                categories.append(pattern.split('|')[0])  # Take first variant
        
        return list(set(categories))
    
    def _extract_intent_from_response(self, response_text: str) -> str:
        """Extract intent from LLM response (simplified parsing)."""
        # In production, would use structured output or better parsing
        intent_match = re.search(r'"intent":\s*"([^"]+)"', response_text)
        if intent_match:
            return intent_match.group(1)
        
        # Fallback to pattern matching in response
        if "find_service" in response_text.lower():
            return "find_service"
        elif "book_service" in response_text.lower():
            return "book_service"
        elif "view_bookings" in response_text.lower():
            return "view_bookings"
        elif "greetings" in response_text.lower():
            return "greetings"
        else:
            return "generic_chat"
    
    def _extract_confidence_from_response(self, response_text: str) -> float:
        """Extract confidence from LLM response."""
        confidence_match = re.search(r'"confidence":\s*([0-9.]+)', response_text)
        if confidence_match:
            try:
                return float(confidence_match.group(1))
            except ValueError:
                pass
        
        return 0.7  # Default confidence for LLM responses
