{"dependencies": ["."], "graphs": {"catchup": "./src/agent/graph.py:graph", "agent_stream": "./src/agent_stream/implementation.py:agent"}, "env": ".env", "image_distro": "wolfi", "input_schema": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["human", "ai", "system"]}, "content": {"type": "string"}}, "required": ["type", "content"]}, "description": "Array of messages in the conversation", "default": [{"type": "human", "content": "Cerco per i prossimi giorni, un aperitivo a Roma"}]}, "user_context": {"type": "object", "properties": {"user_id": {"type": "string", "description": "Unique identifier for the user", "default": "fe95e629-0a4e-474b-97d1-fafe9d6863e3"}, "email_address": {"type": "string", "format": "email", "description": "User's email address", "default": "<EMAIL>"}, "location": {"type": "object", "properties": {"latitude": {"type": "number", "description": "User's latitude position", "default": 45.4666}, "longitude": {"type": "number", "description": "User's longitude position", "default": 9.1832}}}, "current_session_start": {"type": "string", "format": "date-time", "description": "Session start timestamp", "default": "2024-01-15T10:30:00"}}, "required": ["user_id"]}, "session_config": {"type": "object", "properties": {"session_id": {"type": "string", "description": "Memory session identifier", "default": "1233"}, "memory_budget": {"type": "integer", "default": 15, "description": "Memory length parameter"}, "max_tool_calls_per_turn": {"type": "integer", "default": 3}, "conversation_timeout": {"type": "integer", "default": 30}, "preferred_response_style": {"type": "string", "enum": ["conversational", "json"], "default": "conversational"}, "enable_proactive_suggestions": {"type": "boolean", "default": true}}, "required": ["session_id"]}, "current_phase": {"type": "string", "enum": ["GREETING", "INTENT_DISCOVERY", "INFORMATION_GATHERING", "TOOL_EXECUTION", "RESULT_PRESENTATION"], "default": "GREETING"}, "conversation_metrics": {"type": "object", "properties": {"message_count": {"type": "integer", "default": 1}, "tool_calls_count": {"type": "integer", "default": 0}, "successful_tool_calls": {"type": "integer", "default": 0}, "failed_tool_calls": {"type": "integer", "default": 0}, "conversation_start": {"type": "string", "format": "date-time", "default": "2024-01-15T10:30:00"}, "last_activity": {"type": "string", "format": "date-time", "default": "2024-01-15T10:30:00"}, "estimated_tokens_used": {"type": "integer", "default": 0}, "user_satisfaction_indicators": {"type": "array", "items": {"type": "string"}, "default": []}}}}, "required": ["messages", "user_context", "session_config", "current_phase", "conversation_metrics"]}}