"""Quality Assurance Agent for response validation and improvement."""

from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass
from langchain_core.messages import AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext
from agent.state import CatchUpState
from shared import create_llm


class QualityMetric(Enum):
    """Quality metrics for response evaluation."""
    ACCURACY = "accuracy"
    COMPLETENESS = "completeness"
    RELEVANCE = "relevance"
    CLARITY = "clarity"
    BRAND_CONSISTENCY = "brand_consistency"
    HELPFULNESS = "helpfulness"
    TONE = "tone"


@dataclass
class QualityScore:
    """Quality score for a specific metric."""
    metric: QualityMetric
    score: float  # 0.0 to 1.0
    feedback: str
    suggestions: List[str]


@dataclass
class QualityAssessment:
    """Complete quality assessment of a response."""
    overall_score: float
    metric_scores: List[QualityScore]
    improvement_needed: bool
    improved_response: Optional[str] = None
    quality_issues: List[str] = None


class QualityAssuranceAgent(BaseAgent):
    """Agent specialized in response validation and improvement."""
    
    def __init__(self):
        super().__init__(AgentType.QUALITY_ASSURANCE, "Quality Assurance Agent")
        self.quality_standards = self._initialize_quality_standards()
        self.brand_guidelines = self._initialize_brand_guidelines()
        self.improvement_threshold = 0.7  # Responses below this score get improved
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Validate and improve response quality."""
        
        if not context or not context.metadata:
            return {"messages": [AIMessage(content="No response to evaluate.")]}
        
        # Get the original response to evaluate
        original_response = context.metadata.get("original_response", {})
        domain_agent = context.metadata.get("domain_agent", "unknown")
        
        if not original_response:
            return {"messages": [AIMessage(content="No response provided for quality assessment.")]}
        
        # Perform quality assessment
        assessment = await self._assess_response_quality(original_response, state, config, context)
        
        # Determine if improvement is needed
        if assessment.improvement_needed:
            # Generate improved response
            improved_response = await self._improve_response(original_response, assessment, state, config)
            assessment.improved_response = improved_response
            
            return {
                "messages": [AIMessage(content=improved_response)],
                "quality_assessment": assessment,
                "improved_response": improved_response,
                "original_response": original_response
            }
        else:
            # Return original response with quality metrics
            original_messages = original_response.get("messages", [])
            return {
                "messages": original_messages,
                "quality_assessment": assessment,
                "quality_approved": True
            }
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Quality assurance agent handles quality evaluation requests."""
        return intent in ["quality_check", "validate_response", "improve_response"]
    
    async def _assess_response_quality(
        self, 
        response: Dict[str, Any], 
        state: CatchUpState, 
        config: RunnableConfig,
        context: AgentContext
    ) -> QualityAssessment:
        """Assess the quality of a response across multiple metrics."""
        
        # Extract response content
        response_content = self._extract_response_content(response)
        
        # Evaluate each quality metric
        metric_scores = []
        
        # Accuracy assessment
        accuracy_score = await self._assess_accuracy(response_content, state, config)
        metric_scores.append(accuracy_score)
        
        # Completeness assessment
        completeness_score = self._assess_completeness(response_content, context)
        metric_scores.append(completeness_score)
        
        # Relevance assessment
        relevance_score = self._assess_relevance(response_content, state, context)
        metric_scores.append(relevance_score)
        
        # Clarity assessment
        clarity_score = self._assess_clarity(response_content)
        metric_scores.append(clarity_score)
        
        # Brand consistency assessment
        brand_score = self._assess_brand_consistency(response_content)
        metric_scores.append(brand_score)
        
        # Helpfulness assessment
        helpfulness_score = self._assess_helpfulness(response_content, context)
        metric_scores.append(helpfulness_score)
        
        # Tone assessment
        tone_score = self._assess_tone(response_content)
        metric_scores.append(tone_score)
        
        # Calculate overall score
        overall_score = sum(score.score for score in metric_scores) / len(metric_scores)
        
        # Determine if improvement is needed
        improvement_needed = overall_score < self.improvement_threshold
        
        # Identify quality issues
        quality_issues = [
            score.feedback for score in metric_scores 
            if score.score < 0.6  # Issues for scores below 0.6
        ]
        
        return QualityAssessment(
            overall_score=overall_score,
            metric_scores=metric_scores,
            improvement_needed=improvement_needed,
            quality_issues=quality_issues
        )
    
    async def _assess_accuracy(self, response_content: str, state: CatchUpState, config: RunnableConfig) -> QualityScore:
        """Assess response accuracy using LLM."""
        
        try:
            configuration = config.get("configurable", {})
            model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")
            llm = create_llm(model_name)
            
            accuracy_prompt = f"""Evaluate the accuracy of this customer service response:

Response: "{response_content}"

Consider:
- Are any facts or claims verifiable and correct?
- Are there any obvious errors or inconsistencies?
- Does the response make unrealistic promises?
- Are technical details accurate?

Rate accuracy from 0.0 to 1.0 and provide brief feedback.
Format: SCORE: X.X | FEEDBACK: your feedback"""
            
            accuracy_response = await llm.ainvoke([SystemMessage(content=accuracy_prompt)])
            score, feedback = self._parse_llm_evaluation(accuracy_response.content)
            
            return QualityScore(
                metric=QualityMetric.ACCURACY,
                score=score,
                feedback=feedback,
                suggestions=["Verify facts before stating them", "Avoid making promises that can't be kept"]
            )
            
        except Exception as e:
            print(f"Accuracy assessment error: {e}")
            return QualityScore(
                metric=QualityMetric.ACCURACY,
                score=0.8,  # Default score
                feedback="Could not assess accuracy automatically",
                suggestions=[]
            )
    
    def _assess_completeness(self, response_content: str, context: AgentContext) -> QualityScore:
        """Assess response completeness."""
        
        user_intent = context.user_intent or "unknown"
        
        # Check if response addresses the user's intent
        completeness_score = 0.5  # Base score
        
        # Intent-specific completeness checks
        if user_intent == "find_service":
            if any(word in response_content.lower() for word in ["found", "results", "available"]):
                completeness_score += 0.3
            if any(word in response_content.lower() for word in ["price", "location", "time"]):
                completeness_score += 0.2
        
        elif user_intent == "book_service":
            if any(word in response_content.lower() for word in ["booking", "confirmed", "reservation"]):
                completeness_score += 0.3
            if any(word in response_content.lower() for word in ["date", "time", "details"]):
                completeness_score += 0.2
        
        elif user_intent == "view_bookings":
            if any(word in response_content.lower() for word in ["booking", "reservation", "appointment"]):
                completeness_score += 0.5
        
        # General completeness indicators
        if len(response_content) > 50:  # Adequate length
            completeness_score += 0.1
        
        completeness_score = min(completeness_score, 1.0)
        
        feedback = "Response addresses the user's request" if completeness_score > 0.7 else "Response may be incomplete"
        
        return QualityScore(
            metric=QualityMetric.COMPLETENESS,
            score=completeness_score,
            feedback=feedback,
            suggestions=["Include all relevant details", "Address all parts of the user's question"]
        )
    
    def _assess_relevance(self, response_content: str, state: CatchUpState, context: AgentContext) -> QualityScore:
        """Assess response relevance to user's query."""
        
        # Get user's original message
        messages = state.get("messages", [])
        user_query = ""
        if messages:
            # Find the last human message
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == 'human':
                    user_query = msg.content
                    break
        
        relevance_score = 0.5  # Base score
        
        if user_query:
            # Simple keyword matching for relevance
            user_keywords = set(user_query.lower().split())
            response_keywords = set(response_content.lower().split())
            
            # Calculate keyword overlap
            overlap = len(user_keywords.intersection(response_keywords))
            if len(user_keywords) > 0:
                keyword_relevance = min(overlap / len(user_keywords), 1.0)
                relevance_score = max(relevance_score, keyword_relevance)
        
        # Check for generic responses (lower relevance)
        generic_phrases = ["i apologize", "please try again", "contact support"]
        if any(phrase in response_content.lower() for phrase in generic_phrases):
            relevance_score *= 0.8
        
        feedback = "Response is relevant to user's query" if relevance_score > 0.6 else "Response may not fully address user's query"
        
        return QualityScore(
            metric=QualityMetric.RELEVANCE,
            score=relevance_score,
            feedback=feedback,
            suggestions=["Stay focused on user's specific question", "Avoid generic responses"]
        )
    
    def _assess_clarity(self, response_content: str) -> QualityScore:
        """Assess response clarity and readability."""
        
        clarity_score = 0.5  # Base score
        
        # Check sentence length (shorter is generally clearer)
        sentences = response_content.split('.')
        avg_sentence_length = sum(len(s.split()) for s in sentences) / max(len(sentences), 1)
        
        if avg_sentence_length < 20:  # Good sentence length
            clarity_score += 0.2
        elif avg_sentence_length > 30:  # Too long
            clarity_score -= 0.1
        
        # Check for clear structure
        if any(marker in response_content for marker in ['•', '-', '1.', '2.', '\n']):
            clarity_score += 0.2  # Structured content
        
        # Check for jargon or complex terms
        complex_words = ['implementation', 'configuration', 'optimization', 'infrastructure']
        if any(word in response_content.lower() for word in complex_words):
            clarity_score -= 0.1
        
        # Check for clear action items
        action_words = ['please', 'you can', 'to do this', 'next step']
        if any(word in response_content.lower() for word in action_words):
            clarity_score += 0.1
        
        clarity_score = max(0.0, min(clarity_score, 1.0))
        
        feedback = "Response is clear and easy to understand" if clarity_score > 0.7 else "Response could be clearer"
        
        return QualityScore(
            metric=QualityMetric.CLARITY,
            score=clarity_score,
            feedback=feedback,
            suggestions=["Use simple language", "Break down complex information", "Use bullet points for lists"]
        )
    
    def _assess_brand_consistency(self, response_content: str) -> QualityScore:
        """Assess brand consistency."""
        
        brand_score = 0.7  # Base score (assume good unless issues found)
        
        # Check for brand-appropriate tone
        positive_indicators = ['happy to help', 'glad to assist', 'pleasure', 'excellent']
        if any(indicator in response_content.lower() for indicator in positive_indicators):
            brand_score += 0.1
        
        # Check for inappropriate language
        inappropriate = ['unfortunately', 'sorry', 'problem', 'issue', 'error']
        inappropriate_count = sum(1 for word in inappropriate if word in response_content.lower())
        if inappropriate_count > 2:
            brand_score -= 0.2
        
        # Check for CatchUp brand mentions
        if 'catchup' in response_content.lower():
            brand_score += 0.1
        
        # Check for professional tone
        if response_content.startswith(('I', 'We', 'Our')):
            brand_score += 0.1
        
        brand_score = max(0.0, min(brand_score, 1.0))
        
        feedback = "Response aligns with brand guidelines" if brand_score > 0.7 else "Response may not align with brand tone"
        
        return QualityScore(
            metric=QualityMetric.BRAND_CONSISTENCY,
            score=brand_score,
            feedback=feedback,
            suggestions=["Maintain positive tone", "Use brand-appropriate language", "Be helpful and professional"]
        )
    
    def _assess_helpfulness(self, response_content: str, context: AgentContext) -> QualityScore:
        """Assess response helpfulness."""
        
        helpfulness_score = 0.5  # Base score
        
        # Check for actionable information
        action_indicators = ['you can', 'to do this', 'here\'s how', 'follow these steps', 'click', 'visit']
        if any(indicator in response_content.lower() for indicator in action_indicators):
            helpfulness_score += 0.3
        
        # Check for specific information (not vague)
        specific_indicators = ['at', 'on', 'by', 'contact', 'email', 'phone']
        if any(indicator in response_content.lower() for indicator in specific_indicators):
            helpfulness_score += 0.2
        
        # Check for alternatives or suggestions
        alternative_indicators = ['alternatively', 'you could also', 'another option', 'or you can']
        if any(indicator in response_content.lower() for indicator in alternative_indicators):
            helpfulness_score += 0.2
        
        # Penalize for vague responses
        vague_indicators = ['maybe', 'perhaps', 'might', 'possibly', 'not sure']
        if any(indicator in response_content.lower() for indicator in vague_indicators):
            helpfulness_score -= 0.2
        
        helpfulness_score = max(0.0, min(helpfulness_score, 1.0))
        
        feedback = "Response provides helpful guidance" if helpfulness_score > 0.7 else "Response could be more helpful"
        
        return QualityScore(
            metric=QualityMetric.HELPFULNESS,
            score=helpfulness_score,
            feedback=feedback,
            suggestions=["Provide specific steps", "Offer alternatives", "Include contact information when relevant"]
        )
    
    def _assess_tone(self, response_content: str) -> QualityScore:
        """Assess response tone appropriateness."""
        
        tone_score = 0.6  # Base score
        
        # Check for friendly tone
        friendly_indicators = ['happy', 'glad', 'pleased', 'delighted', 'welcome']
        if any(indicator in response_content.lower() for indicator in friendly_indicators):
            tone_score += 0.2
        
        # Check for professional tone
        professional_indicators = ['assist', 'help', 'support', 'service', 'provide']
        if any(indicator in response_content.lower() for indicator in professional_indicators):
            tone_score += 0.1
        
        # Check for empathy
        empathy_indicators = ['understand', 'appreciate', 'realize', 'know how']
        if any(indicator in response_content.lower() for indicator in empathy_indicators):
            tone_score += 0.1
        
        # Penalize for negative tone
        negative_indicators = ['can\'t', 'won\'t', 'impossible', 'never', 'no way']
        if any(indicator in response_content.lower() for indicator in negative_indicators):
            tone_score -= 0.2
        
        tone_score = max(0.0, min(tone_score, 1.0))
        
        feedback = "Tone is appropriate and professional" if tone_score > 0.7 else "Tone could be more positive"
        
        return QualityScore(
            metric=QualityMetric.TONE,
            score=tone_score,
            feedback=feedback,
            suggestions=["Use positive language", "Show empathy", "Maintain professional friendliness"]
        )
    
    async def _improve_response(
        self, 
        original_response: Dict[str, Any], 
        assessment: QualityAssessment, 
        state: CatchUpState, 
        config: RunnableConfig
    ) -> str:
        """Generate an improved version of the response."""
        
        original_content = self._extract_response_content(original_response)
        
        # Identify main issues
        main_issues = [score.feedback for score in assessment.metric_scores if score.score < 0.6]
        
        # Generate improvement suggestions
        all_suggestions = []
        for score in assessment.metric_scores:
            all_suggestions.extend(score.suggestions)
        
        try:
            configuration = config.get("configurable", {})
            model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")
            llm = create_llm(model_name)
            
            improvement_prompt = f"""Improve this customer service response based on the quality issues identified:

Original Response: "{original_content}"

Quality Issues:
{chr(10).join(f"- {issue}" for issue in main_issues)}

Improvement Guidelines:
{chr(10).join(f"- {suggestion}" for suggestion in set(all_suggestions))}

Generate an improved response that:
1. Maintains the original intent and information
2. Addresses the quality issues
3. Is more helpful, clear, and professional
4. Follows CatchUp brand guidelines (friendly, helpful, professional)

Improved Response:"""
            
            improved_response = await llm.ainvoke([SystemMessage(content=improvement_prompt)])
            
            # Extract the improved response (remove any prefixes)
            improved_content = improved_response.content
            if "Improved Response:" in improved_content:
                improved_content = improved_content.split("Improved Response:")[-1].strip()
            
            return improved_content
            
        except Exception as e:
            print(f"Response improvement error: {e}")
            # Return original with minor improvements
            return f"Thank you for your question. {original_content} Please let me know if you need any additional assistance!"
    
    def _extract_response_content(self, response: Dict[str, Any]) -> str:
        """Extract text content from response."""
        
        if isinstance(response, dict):
            messages = response.get("messages", [])
            if messages:
                # Get the last AI message
                for msg in reversed(messages):
                    if hasattr(msg, 'content'):
                        return msg.content
                    elif isinstance(msg, dict) and 'content' in msg:
                        return msg['content']
        
        return str(response)
    
    def _parse_llm_evaluation(self, evaluation_text: str) -> tuple[float, str]:
        """Parse LLM evaluation response."""
        
        try:
            # Look for SCORE: X.X pattern
            import re
            score_match = re.search(r'SCORE:\s*([0-9.]+)', evaluation_text)
            feedback_match = re.search(r'FEEDBACK:\s*(.+)', evaluation_text)
            
            score = float(score_match.group(1)) if score_match else 0.7
            feedback = feedback_match.group(1).strip() if feedback_match else "Evaluation completed"
            
            return min(max(score, 0.0), 1.0), feedback
            
        except Exception:
            return 0.7, "Could not parse evaluation"
    
    def _initialize_quality_standards(self) -> Dict[str, Dict[str, Any]]:
        """Initialize quality standards for each metric."""
        return {
            "accuracy": {"min_score": 0.8, "weight": 0.2},
            "completeness": {"min_score": 0.7, "weight": 0.15},
            "relevance": {"min_score": 0.8, "weight": 0.2},
            "clarity": {"min_score": 0.7, "weight": 0.15},
            "brand_consistency": {"min_score": 0.8, "weight": 0.1},
            "helpfulness": {"min_score": 0.7, "weight": 0.15},
            "tone": {"min_score": 0.7, "weight": 0.05}
        }
    
    def _initialize_brand_guidelines(self) -> Dict[str, Any]:
        """Initialize brand guidelines for consistency checking."""
        return {
            "tone": "friendly, professional, helpful",
            "voice": "conversational but professional",
            "avoid": ["technical jargon", "negative language", "vague responses"],
            "include": ["specific actions", "clear next steps", "empathy"],
            "brand_values": ["customer-first", "reliability", "innovation", "accessibility"]
        }
