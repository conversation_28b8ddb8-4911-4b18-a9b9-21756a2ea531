"""Communication Agent for multi-channel user communication."""

from typing import Dict, Any, Optional, List
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig

from .base_agent import BaseAgent, AgentType, AgentContext
from agent.state import CatchUpState
from agent.tools.tool_manager import ToolCategory


class CommunicationAgent(BaseAgent):
    """Agent specialized in multi-channel user communication."""
    
    def __init__(self):
        super().__init__(AgentType.COMMUNICATION, "Communication Agent")
        self.required_tool_categories = {
            ToolCategory.COMMUNICATION
        }
        self.communication_templates = self._initialize_templates()
        self.channel_preferences = self._initialize_channel_preferences()
    
    async def process(
        self, 
        state: CatchUpState, 
        config: RunnableConfig,
        context: Optional[AgentContext] = None
    ) -> Dict[str, Any]:
        """Process communication requests with template management and delivery tracking."""
        
        # Load tools if not already loaded
        if not self._tools:
            await self.load_tools()
        
        # Determine communication type and channel
        comm_request = self._analyze_communication_request(state, context)
        
        # Validate communication parameters
        validation_result = self._validate_communication_request(comm_request, state)
        if not validation_result["valid"]:
            return {
                "messages": [AIMessage(content=validation_result["message"])],
                "validation_error": validation_result["message"]
            }
        
        # Execute communication
        result = await self._execute_communication(comm_request, state, config)
        
        return result
    
    def can_handle(self, intent: str, context: Optional[AgentContext] = None) -> bool:
        """Check if this agent can handle communication-related intents."""
        communication_intents = [
            "send_email", "send_whatsapp", "send_sms", "communication",
            "notify_user", "send_confirmation", "send_reminder"
        ]
        return intent in communication_intents
    
    def _analyze_communication_request(self, state: CatchUpState, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Analyze the communication request to determine type and channel."""
        
        messages = state.get("messages", [])
        if not messages:
            return {"type": "unknown", "channel": "email"}  # Default
        
        last_message = messages[-1]
        message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)
        message_lower = message_content.lower()
        
        # Determine communication type
        comm_type = "general"
        if any(word in message_lower for word in ["conferma", "confirmation", "booking"]):
            comm_type = "booking_confirmation"
        elif any(word in message_lower for word in ["promemoria", "reminder", "remind"]):
            comm_type = "reminder"
        elif any(word in message_lower for word in ["offerta", "offer", "promotion", "deal"]):
            comm_type = "promotional"
        elif any(word in message_lower for word in ["notifica", "notification", "alert"]):
            comm_type = "notification"
        
        # Determine preferred channel
        channel = "email"  # Default
        if any(word in message_lower for word in ["whatsapp", "wa", "messaggio"]):
            channel = "whatsapp"
        elif any(word in message_lower for word in ["sms", "text", "telefono"]):
            channel = "sms"
        elif any(word in message_lower for word in ["email", "mail", "e-mail"]):
            channel = "email"
        
        # Extract content and recipient info
        content = self._extract_communication_content(message_content, comm_type)
        recipient_info = self._extract_recipient_info(state, context)
        
        return {
            "type": comm_type,
            "channel": channel,
            "content": content,
            "recipient": recipient_info,
            "original_message": message_content
        }
    
    def _validate_communication_request(self, comm_request: Dict[str, Any], state: CatchUpState) -> Dict[str, Any]:
        """Validate communication request parameters."""
        
        recipient = comm_request.get("recipient", {})
        channel = comm_request.get("channel")
        
        # Check if recipient information is available
        if channel == "email" and not recipient.get("email"):
            return {
                "valid": False,
                "message": "I need your email address to send you an email. Please update your profile or provide your email address."
            }
        
        if channel == "whatsapp" and not recipient.get("phone"):
            return {
                "valid": False,
                "message": "I need your phone number to send you a WhatsApp message. Please complete your profile in the CatchUp app."
            }
        
        if channel == "sms" and not recipient.get("phone"):
            return {
                "valid": False,
                "message": "I need your phone number to send you an SMS. Please update your profile with your phone number."
            }
        
        # Check content
        if not comm_request.get("content"):
            return {
                "valid": False,
                "message": "I need to know what message you'd like me to send. Could you please specify the content?"
            }
        
        return {"valid": True, "message": "Communication request is valid"}
    
    async def _execute_communication(self, comm_request: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Execute the communication request."""
        
        channel = comm_request.get("channel")
        comm_type = comm_request.get("type")
        
        try:
            if channel == "email":
                return await self._send_email(comm_request, state, config)
            elif channel == "whatsapp":
                return await self._send_whatsapp(comm_request, state, config)
            elif channel == "sms":
                return await self._send_sms(comm_request, state, config)
            else:
                return {
                    "messages": [AIMessage(content=f"I don't support {channel} communication yet. Please try email or WhatsApp.")],
                    "unsupported_channel": channel
                }
                
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"I encountered an error while sending your message: {str(e)}")],
                "communication_error": str(e)
            }
    
    async def _send_email(self, comm_request: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Send email communication."""
        
        email_tool = self._get_tool_by_name("sent_email_to_users")
        if not email_tool:
            return {
                "messages": [AIMessage(content="Email service is currently unavailable. Please try again later.")],
                "service_unavailable": "email"
            }
        
        # Prepare email content
        email_content = self._prepare_email_content(comm_request, state)
        
        try:
            email_result = await email_tool.ainvoke(email_content)
            
            success_message = f"✅ Email sent successfully to {comm_request['recipient']['email']}!"
            if comm_request.get("type") == "booking_confirmation":
                success_message += "\n📧 You should receive your booking confirmation shortly."
            
            return {
                "messages": [AIMessage(content=success_message)],
                "email_sent": True,
                "email_result": email_result
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"Failed to send email: {str(e)}")],
                "email_error": str(e)
            }
    
    async def _send_whatsapp(self, comm_request: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Send WhatsApp communication."""
        
        whatsapp_tool = self._get_tool_by_name("whatsapp_sent_tool")
        if not whatsapp_tool:
            return {
                "messages": [AIMessage(content="WhatsApp service is currently unavailable. Please try again later.")],
                "service_unavailable": "whatsapp"
            }
        
        # Prepare WhatsApp content
        whatsapp_content = self._prepare_whatsapp_content(comm_request, state)
        
        try:
            whatsapp_result = await whatsapp_tool.ainvoke(whatsapp_content)
            
            success_message = f"✅ WhatsApp message sent successfully!"
            if comm_request.get("type") == "booking_confirmation":
                success_message += "\n📱 Check your WhatsApp for the booking confirmation."
            
            return {
                "messages": [AIMessage(content=success_message)],
                "whatsapp_sent": True,
                "whatsapp_result": whatsapp_result
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"Failed to send WhatsApp message: {str(e)}")],
                "whatsapp_error": str(e)
            }
    
    async def _send_sms(self, comm_request: Dict[str, Any], state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
        """Send SMS communication."""
        
        sms_tool = self._get_tool_by_name("sms_tool")
        if not sms_tool:
            return {
                "messages": [AIMessage(content="SMS service is currently unavailable. Please try email or WhatsApp instead.")],
                "service_unavailable": "sms"
            }
        
        # Prepare SMS content
        sms_content = self._prepare_sms_content(comm_request, state)
        
        try:
            sms_result = await sms_tool.ainvoke(sms_content)
            
            return {
                "messages": [AIMessage(content="✅ SMS sent successfully!")],
                "sms_sent": True,
                "sms_result": sms_result
            }
            
        except Exception as e:
            return {
                "messages": [AIMessage(content=f"Failed to send SMS: {str(e)}")],
                "sms_error": str(e)
            }
    
    def _extract_communication_content(self, message_content: str, comm_type: str) -> Dict[str, Any]:
        """Extract communication content from the message."""
        
        # Use templates based on communication type
        template = self.communication_templates.get(comm_type, self.communication_templates["general"])
        
        # Extract specific content from message
        content = {
            "subject": template.get("subject", "Message from CatchUp"),
            "body": message_content,
            "template_type": comm_type
        }
        
        # Customize based on type
        if comm_type == "booking_confirmation":
            content["subject"] = "Booking Confirmation - CatchUp"
            content["body"] = "Your booking has been confirmed. Details are attached."
        elif comm_type == "reminder":
            content["subject"] = "Reminder - CatchUp"
            content["body"] = "This is a friendly reminder about your upcoming appointment."
        elif comm_type == "promotional":
            content["subject"] = "Special Offer - CatchUp"
            content["body"] = "Check out this special offer just for you!"
        
        return content
    
    def _extract_recipient_info(self, state: CatchUpState, context: Optional[AgentContext]) -> Dict[str, Any]:
        """Extract recipient information from state."""
        
        user_context = state.get("user_context", {})
        
        return {
            "user_id": user_context.get("user_id"),
            "email": user_context.get("email_address"),
            "phone": user_context.get("phone_number"),
            "name": user_context.get("name", "Valued Customer")
        }
    
    def _prepare_email_content(self, comm_request: Dict[str, Any], state: CatchUpState) -> Dict[str, Any]:
        """Prepare email content for sending."""
        
        content = comm_request.get("content", {})
        recipient = comm_request.get("recipient", {})
        
        return {
            "to_email": recipient.get("email"),
            "subject": content.get("subject", "Message from CatchUp"),
            "body": content.get("body", ""),
            "user_name": recipient.get("name", "Valued Customer"),
            "template_type": content.get("template_type", "general")
        }
    
    def _prepare_whatsapp_content(self, comm_request: Dict[str, Any], state: CatchUpState) -> Dict[str, Any]:
        """Prepare WhatsApp content for sending."""
        
        content = comm_request.get("content", {})
        recipient = comm_request.get("recipient", {})
        
        # WhatsApp messages should be concise and without IDs
        message_body = content.get("body", "")
        
        # Remove any IDs or sensitive information
        clean_message = self._sanitize_message_for_whatsapp(message_body)
        
        return {
            "phone_number": recipient.get("phone"),
            "message": clean_message,
            "user_name": recipient.get("name", "")
        }
    
    def _prepare_sms_content(self, comm_request: Dict[str, Any], state: CatchUpState) -> Dict[str, Any]:
        """Prepare SMS content for sending."""
        
        content = comm_request.get("content", {})
        recipient = comm_request.get("recipient", {})
        
        # SMS should be very concise
        message_body = content.get("body", "")
        
        # Truncate if too long (SMS limit ~160 characters)
        if len(message_body) > 150:
            message_body = message_body[:147] + "..."
        
        return {
            "phone_number": recipient.get("phone"),
            "message": message_body
        }
    
    def _sanitize_message_for_whatsapp(self, message: str) -> str:
        """Remove IDs and sensitive information from WhatsApp messages."""
        
        import re
        
        # Remove common ID patterns
        message = re.sub(r'\b(deal|business|booking|user)Id\s*:?\s*[a-zA-Z0-9\-]+', '', message, flags=re.IGNORECASE)
        message = re.sub(r'\bid\s*:?\s*[a-zA-Z0-9\-]+', '', message, flags=re.IGNORECASE)
        
        # Clean up extra whitespace
        message = re.sub(r'\s+', ' ', message).strip()
        
        return message
    
    def _get_tool_by_name(self, tool_name: str):
        """Get a tool by name from loaded tools."""
        if not self._tools:
            return None
        
        for tool in self._tools:
            if hasattr(tool, 'name') and tool.name == tool_name:
                return tool
        return None
    
    def _initialize_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize communication templates."""
        return {
            "general": {
                "subject": "Message from CatchUp",
                "email_template": "general_email",
                "whatsapp_template": "general_whatsapp"
            },
            "booking_confirmation": {
                "subject": "Booking Confirmation - CatchUp",
                "email_template": "booking_confirmation_email",
                "whatsapp_template": "booking_confirmation_whatsapp"
            },
            "reminder": {
                "subject": "Appointment Reminder - CatchUp",
                "email_template": "reminder_email",
                "whatsapp_template": "reminder_whatsapp"
            },
            "promotional": {
                "subject": "Special Offer - CatchUp",
                "email_template": "promotional_email",
                "whatsapp_template": "promotional_whatsapp"
            },
            "notification": {
                "subject": "Notification - CatchUp",
                "email_template": "notification_email",
                "whatsapp_template": "notification_whatsapp"
            }
        }
    
    def _initialize_channel_preferences(self) -> Dict[str, Dict[str, Any]]:
        """Initialize channel-specific preferences and limitations."""
        return {
            "email": {
                "max_length": 10000,
                "supports_html": True,
                "supports_attachments": True,
                "delivery_tracking": True
            },
            "whatsapp": {
                "max_length": 4096,
                "supports_html": False,
                "supports_attachments": False,
                "delivery_tracking": True,
                "no_ids_policy": True  # Don't include IDs in WhatsApp messages
            },
            "sms": {
                "max_length": 160,
                "supports_html": False,
                "supports_attachments": False,
                "delivery_tracking": False
            }
        }
